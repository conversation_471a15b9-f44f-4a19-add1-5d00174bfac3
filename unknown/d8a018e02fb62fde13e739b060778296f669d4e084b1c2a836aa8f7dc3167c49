package com.enttribe.promptanalyzer.rest;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.HashMap;
import java.util.List;

@FeignClient(
        name = "ToolCallbackProviderRest",
        url = "${prompt-analyzer-service.url}",
        primary = false
)
public interface ToolCallbackProviderRest {

    /**
     * Fetches a list of tools from the external service.
     *
     * @return A list of tools as a list of hash maps.
     */
    @GetMapping("/getListOfTools")
    List<HashMap<String, String>> getToolCallbackProvider();
}
