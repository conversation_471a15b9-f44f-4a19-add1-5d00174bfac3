package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptFooDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing prompt operations.
 * Provides endpoints for creating, updating, searching, and managing prompts,
 * including import/export functionality and variable management.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "PromptRest", url = "${prompt-analyzer-service.url}", path = "/prompt", primary = false)
public interface PromptRest {

    /**
     * Health check endpoint.
     *
     * @return Map containing success status
     */
    @Operation(
            summary = "Health check endpoint",
            description = "Checks the health status of the service."
    )
    @GetMapping(path = "/ping")
    Map<String, String> success();

    /**
     * Saves a new prompt.
     *
     * @param prompt The prompt data to save
     * @return Map containing the result of the save operation
     */
    @Operation(
            summary = "Save a new prompt",
            description = "Saves a new prompt to the system.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Prompt saved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid prompt data")
    })
    @PostMapping(path = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> save(@Valid @RequestBody PromptDto prompt);

    /**
     * Retrieves versions of a prompt based on provided criteria.
     *
     * @param promptRequestDto Contains application, name, category, and status filters
     * @return List of prompt version details
     */
    @Operation(
            summary = "Retrieve versions of a prompt",
            description = "Retrieves all versions of a prompt based on the provided criteria.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Prompt versions retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Prompt not found")
    })
    @PostMapping(path = "/getVersionsOfPrompt", consumes = MediaType.APPLICATION_JSON_VALUE)
    List<PromptVersionDetailsDto> getVersionsOfPrompt(@RequestBody PromptRequestDto promptRequestDto);

    /**
     * Updates an existing prompt.
     *
     * @param prompt The updated prompt data
     * @return Map containing the result of the update operation
     */
    @Operation(
            summary = "Update an existing prompt",
            description = "Updates an existing prompt in the system.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Prompt updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid prompt data"),
            @ApiResponse(responseCode = "404", description = "Prompt not found")
    })
    @PostMapping(path = "/edit", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> edit(@Valid @RequestBody PromptDto prompt);

    /**
     * Deletes a prompt by ID (soft delete).
     * Requires ROLE_API_TOOL_WRITE security role.
     *
     * @param map Contains the ID of the prompt to delete
     * @return Map containing the result of the delete operation
     */
    @Operation(summary = "Delete Prompt by Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TOOL_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Delete Prompt successfully by using given id"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/deleteById")
    Map<String, String> deleteById(@RequestBody Map<String, Integer> map);

    /**
     * Gets basic details of prompts by application.
     *
     * @param map A map containing application details.
     * @return A list of maps containing prompt basic details.
     */
    @Operation(
            summary = "Get prompt basic details by application",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompt basic details retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/getPromptBasicDetailByApplication")
    List<Map<String, String>> getPromptBasicDetailByApplication(@RequestBody Map<String, String> map);

    /**
     * Retrieves detailed prompt information by ID.
     *
     * @param map A map containing the prompt ID.
     * @return A PromptConvertorDto containing the prompt details.
     */
    @Operation(
            summary = "Retrieve detailed prompt information by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompt details retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/viewById", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PromptConvertorDto getPromptById(@RequestBody Map<String, Integer> map);

    /**
     * Retrieves SDK-formatted prompt information by ID.
     *
     * @param id The ID of the prompt to retrieve.
     * @return A PromptDtoSdk containing the prompt details.
     */
    @Operation(
            summary = "Retrieve SDK-formatted prompt information by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "SDK-formatted prompt details retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/getPromptById/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PromptDtoSdk getPromptById(@PathVariable Integer id);

    /**
     * Checks if a prompt exists based on provided criteria.
     *
     * @param promptRequestDto The criteria to check for existence.
     * @return A map containing existence check results.
     */
    @Operation(
            summary = "Check if a prompt exists",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Existence check completed successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/exists", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Object> exists(@RequestBody PromptRequestDto promptRequestDto);

    /**
     * Retrieves distinct application names, optionally filtered.
     *
     * @param applicationName Optional application name filter.
     * @return A list of distinct application names.
     */
    @Operation(
            summary = "Retrieve distinct application names",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Distinct application names retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/getDistinctApplications")
    List<String> getDistinctApplications(@RequestParam(value = "applicationName", required = false) String applicationName);

    /**
     * Retrieves distinct categories for a specific application.
     *
     * @param applicationName The application name to get categories for.
     * @return A list of distinct categories.
     */
    @Operation(
            summary = "Retrieve distinct categories for a specific application",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Distinct categories retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/getDistinctCategoriesByApp")
    List<String> getDistinctCategoriesByApp(@RequestParam String applicationName);

    /**
     * Filters prompts based on provided criteria.
     *
     * @param filterMap A map containing filter criteria.
     * @return A list of filtered prompts as PromptFooDto objects.
     */
    @Operation(
            summary = "Filter prompts based on criteria",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Filtered prompts retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/filter")
    List<PromptFooDto> filter(@RequestBody Map<String, Object> filterMap);

    /**
     * Updates the assertion template for a prompt.
     *
     * @param assertionTemplateDto The updated assertion template data.
     * @return A map containing the result of the update operation.
     */
    @Operation(
            summary = "Update the assertion template for a prompt",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Assertion template updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/updateAssertionTemplate", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> updateAssertionTemplate(@RequestBody AssertionTemplateDto assertionTemplateDto);

    /**
     * Searches for prompts with pagination and sorting options.
     *
     * @param filter    Optional filter criteria for searching prompts.
     * @param offset    Required pagination offset.
     * @param size      Required pagination size.
     * @param orderBy   Optional field to order results by.
     * @param orderType Optional order direction (asc/desc).
     * @return A list of matching prompts as PromptConvertorDto objects.
     */
    @Operation(
            summary = "Search for prompts with pagination and sorting",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompts retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/search")
    List<PromptConvertorDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Version 1 of prompt search with pagination and sorting options.
     *
     * @param filter    Optional filter criteria for searching prompts.
     * @param offset    Required pagination offset.
     * @param size      Required pagination size.
     * @param orderBy   Optional field to order results by.
     * @param orderType Optional order direction (asc/desc).
     * @return A list of matching prompts as PromptFooDto objects.
     */
    @Operation(
            summary = "Version 1 of prompt search",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompts retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/v1/search")
    List<PromptFooDto> searchV1(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts the number of prompts matching the optional filter.
     *
     * @param filter Optional filter criteria.
     * @return Total count of matching prompts.
     */
    @Operation(
            summary = "Count the number of prompts",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/count")
    Long count(@RequestParam(required = false) String filter);

    /**
     * Retrieves prompts for a specific application in SDK format.
     *
     * @param appName The application name to get prompts for.
     * @return A list of prompts as PromptDtoSdk objects.
     */
    @Operation(
            summary = "Retrieve prompts for a specific application in SDK format",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompts retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/getPromptByApplication/{appName}")
    List<PromptDtoSdk> getPromptByApplication(@PathVariable String appName);

    /**
     * Exports prompts for an application as CSV.
     *
     * @param appName The application name to export prompts for.
     * @return ResponseEntity containing the CSV resource.
     */
    @Operation(
            summary = "Export prompts for an application as CSV",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "CSV export successful"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(value = "/exportPrompt/{appName}", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<Resource> exportCSV(@PathVariable String appName);

    /**
     * Exports prompts as CSV based on a list of prompt IDs.
     *
     * @param promptIds List of prompt IDs to export
     * @return CSV file as a downloadable resource
     */
    @Operation(
            summary = "Export prompts as CSV by IDs",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Prompts exported successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(value = "/exportPromptsByIds", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<Resource> exportPromptsByIds(@RequestBody List<Integer> promptIds);


    /**
     * Imports prompts from a CSV file.
     *
     * @param file The CSV file containing prompt data.
     * @return ResponseEntity containing the import result.
     */
    @Operation(
            summary = "Import prompts from a CSV file",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Import successful"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(value = "/importPrompt", produces = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<Resource> importPrompt(@RequestParam("file") MultipartFile file);

    /**
     * Updates a tag by its ID.
     *
     * @param id   The ID of the tag to update.
     * @param tags A map containing the updated tag data.
     * @return A map containing the result of the update operation.
     */
    @Operation(
            summary = "Update tag by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_PROMPT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/updateTagById/{id}")
    Map<String, String> updateTagById(@PathVariable Integer id, @RequestBody Map<String, String> tags);

    @GetMapping(path = "/findByName/{promptName}")
    PromptDto getPromptByName(@PathVariable String promptName);

    @GetMapping(path = "/findPromptById/{id}")
    PromptDto findPromptById(@PathVariable  Integer id);


}


