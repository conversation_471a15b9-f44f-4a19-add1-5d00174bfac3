package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/*
 * REST client interface for hint management operations.
 * Provides CRUD operations for managing hints through Feign client.
 * Author: VisionWaves
 * Version: 1.0
 */
@FeignClient(name = "AgentRest", url = "${prompt-analyzer-service.url}", path = "/hint", primary = false)
public interface HintRest {

    /**
     * Saves hints based on the provided request map.
     *
     * @param requestMap The request map containing hint data
     * @return Map containing the result of the save operation
     */
    @Operation(
            summary = "Save hints",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/save")
    Map<String, Boolean> saveHints(@RequestBody Map<String, Object> requestMap);

    @Operation(
            summary = "Search for hints",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/search")
    Map<String, String> searchPlan(@RequestBody Map<String, String> map);

    @Operation(
            summary = "Search for hints in batch",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_HINT_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Hints search successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/search-batch")
    List<Map<String, Object>> searchPlanBatch(@RequestBody List<String> queries);

    @PostMapping("/search-batch/{type}")
    List<Map<String, Object>> searchPlanBatchV1(@RequestBody List<String> queries, @PathVariable String type);

}
