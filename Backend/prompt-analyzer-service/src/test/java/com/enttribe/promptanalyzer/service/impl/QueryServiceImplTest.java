package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.QueryDao;
import com.enttribe.promptanalyzer.dto.query.QueryRequestDto;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Query;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest

class QueryServiceImplTest {

   @MockitoBean
    private CustomFilter customFilter;

   @MockitoBean
    private QueryDao queryDao;

    @Autowired
    private QueryServiceImpl queryServiceImpl;

    private Query mockQuery;
    private QueryRequestDto mockQueryDto;

    @BeforeEach
    void setUp() {
        mockQuery = new Query();
        mockQuery.setId(1);
        mockQuery.setQuestion("Sample Question?");
        mockQuery.setUserId("123");
        mockQuery.setType("General");
        mockQuery.setCreatedTime(new Date());
        mockQuery.setDeleted(false);

        mockQueryDto = new QueryRequestDto();
        mockQueryDto.setQuestion("Sample Question?");
        mockQueryDto.setUserId("123");
        mockQueryDto.setType("General");
    }

    @Test
    @DisplayName("Search query success")
    void querySearchSuccess() {
        when(customFilter.searchByFilter(eq(Query.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(List.of(mockQuery));
        List<QueryResponseDto> result = queryServiceImpl.search("filter", 0, 10, "orderBy", "ASC");
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(customFilter, times(1)).searchByFilter(eq(Query.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Search query failure")
    void querySearchFailure() {
        when(customFilter.searchByFilter(eq(Query.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenThrow(new BusinessException("Test exception"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> queryServiceImpl.search("filter", 0, 10, "orderBy", "ASC"));
        assertEquals("Test exception", exception.getMessage());
        verify(customFilter, times(1)).searchByFilter(eq(Query.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Count query success")
    void queryCount() {
        when(customFilter.countByFilter(eq(Query.class), anyString())).thenReturn(1L);
        Long result = queryServiceImpl.count("filter");
        assertEquals(1L, result);
        verify(customFilter, times(1)).countByFilter(eq(Query.class), anyString());
    }

    @Test
    @DisplayName("Create query success")
    void createQuerySuccess(){
        when(queryDao.save(any(Query.class))).thenReturn(mockQuery);
        Map<String, String> result = queryServiceImpl.createQuery(mockQueryDto);
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        verify(queryDao, times(1)).save(any(Query.class));
    }

    @Test
    @DisplayName("Create query failure")
    void createQueryFailure() {
        when(queryDao.save(any(Query.class))).thenThrow(new BusinessException("Test exception"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> queryServiceImpl.createQuery(mockQueryDto));
        assertEquals("Unable to save Query: Test exception", exception.getMessage());
        verify(queryDao, times(1)).save(any(Query.class));
    }

    @Test
    @DisplayName("Soft delete query success")
    void querySoftDelete(){
        when(queryDao.findById(anyInt())).thenReturn(Optional.of(mockQuery));
        when(queryDao.save(any(Query.class))).thenReturn(mockQuery);
        Map<String, String> result = queryServiceImpl.softDelete(1);
        assertEquals("success", result.get("result"));
        verify(queryDao, times(1)).findById(1);
        verify(queryDao, times(1)).save(mockQuery);
    }

    @Test
    @DisplayName("Soft delete query failure")
    void querySoftDeleteFailure() {
        when(queryDao.findById(anyInt())).thenReturn(Optional.empty());
        Map<String, String> result = queryServiceImpl.softDelete(1);
        assertNotNull(result);
        assertEquals("failed", result.get("result"));
        verify(queryDao, times(1)).findById(1);
    }
}