package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.ProcessorDao;
import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Processor;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.jupiter.api.Assertions.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProcessorServiceImplTest {
    @Mock
    ProcessorDao processorDao;
    @Mock
    CustomFilter customFilter;
    @InjectMocks
    ProcessorServiceImpl processorServiceImpl;

    private Processor mockProcessor;
    private ProcessorRequestDto mockProcessorDto;

    @BeforeEach
    void setUp() {
        mockProcessor = new Processor();
        mockProcessor.setId(1);
        mockProcessor.setKey("sample Key");
        mockProcessor.setDisplayName("Sample Processor");
        mockProcessor.setIcon("icon");
        mockProcessor.setStyleType("type1");
        mockProcessor.setCategory("categoryA");
        mockProcessor.setSubCategory("subcategoryA");
        mockProcessor.setCreatedTime(new Date());
        mockProcessor.setModifiedTime(new Date());

        mockProcessorDto = new ProcessorRequestDto();
        mockProcessorDto.setKey("sample Key");
        mockProcessorDto.setDisplayName("Sample Processor");
        mockProcessorDto.setIcon("icon");
        mockProcessorDto.setStyleType("type1");
        mockProcessorDto.setCategory("categoryA");
        mockProcessorDto.setSubCategory("subcategoryA");
    }

    @Test
    @DisplayName("Test save success")
    void save() {
        when(processorDao.save(any(Processor.class))).thenReturn(mockProcessor);
        Map<String, Object> result = processorServiceImpl.save(mockProcessorDto);
        assertNotNull(result);
        assertEquals("success", result.get("result"));
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("save failure DataAccessException")
    void saveFailureDuplicateEntry() {
        DataAccessException dataAccessException = new DataAccessException("unique_key violation") {};
        when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.save(mockProcessorDto));
        assertTrue(thrown.getMessage().contains("duplicate entry for sample Key"));
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("Save failure DataAccessException")
    void saveFailureDataAccessException() {
        DataAccessException dataAccessException = new DataAccessException("Some DB Error") {};
        when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
        BusinessException thrown = assertThrows(BusinessException.class, () -> processorServiceImpl.save(mockProcessorDto));
        assertEquals("Some DB Error", thrown.getMessage());
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("save failure Business Exception")
    void saveFailureBusinessException() {
        when(processorDao.save(any(Processor.class))).thenThrow(new BusinessException("Unexpected Error"));
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.save(mockProcessorDto));
        assertTrue(thrown.getMessage().contains("Unexpected Error"));
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("update success")
    void updateSuccess() {
        when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
        when(processorDao.save(any(Processor.class))).thenReturn(mockProcessor);
        Map<String, Object> result = processorServiceImpl.update(1, mockProcessorDto);
        assertNotNull(result);
        assertEquals("success", result.get("result"));
        verify(processorDao, times(1)).findById(1);
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("update failure BusinessException")
    void updateFailure() {
        when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
        when(processorDao.save(any(Processor.class))).thenThrow(new BusinessException("Unexpected Error"));
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.update(1, mockProcessorDto));
        assertTrue(thrown.getMessage().contains("Unexpected Error"));
        verify(processorDao, times(1)).findById(1);
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("Update failure Processor Not Found")
    void updateFailureNotFound() {
        when(processorDao.findById(1)).thenReturn(Optional.empty());
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.update(1, mockProcessorDto));
        assertEquals("processor is not found with id : 1", thrown.getMessage());
        verify(processorDao, times(1)).findById(1);
        verify(processorDao, never()).save(any(Processor.class)); // Ensure `save` is NOT called
    }


    @Test
    @DisplayName("Update failure DataAccessException (Duplicate Key)")
    void updateFailureDuplicateEntry() {
        when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
        DataAccessException dataAccessException = new DataAccessException("unique_key violation") {};
        when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.update(1, mockProcessorDto));
        assertEquals("duplicate entry for sample Key", thrown.getMessage());
        verify(processorDao, times(1)).findById(1);
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("Update failure DataAccessException (Other DB Error)")
    void updateFailureDataAccessException() {
        when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
        DataAccessException dataAccessException = new DataAccessException("Some DB Error") {};
        when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
        BusinessException thrown = assertThrows(BusinessException.class,
                () -> processorServiceImpl.update(1, mockProcessorDto));
        assertEquals("Some DB Error", thrown.getMessage());
        verify(processorDao, times(1)).findById(1);
        verify(processorDao, times(1)).save(any(Processor.class));
    }

    @Test
    @DisplayName("Search processor success")
    void searchSuccess() {
        when(customFilter.searchByFilter(eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(List.of(mockProcessor));
        List<ProcessorResponseDto> result = processorServiceImpl.search("filter", 0, 10, "orderBy", "ASC");
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(customFilter, times(1)).searchByFilter(eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Search processor with null filter")
    void searchWithNullFilter() {
        when(customFilter.searchByFilter(eq(Processor.class), isNull(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(List.of(mockProcessor));
        List<ProcessorResponseDto> result = processorServiceImpl.search(null, 0, 10, "orderBy", "ASC");
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(customFilter, times(1)).searchByFilter(eq(Processor.class), isNull(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Search processor failure")
    void searchFailure() {
        when(customFilter.searchByFilter(eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenThrow(new BusinessException("Test exception"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> processorServiceImpl.search("filter", 0, 10, "orderBy", "ASC"));
        assertEquals("Test exception", exception.getMessage());
        verify(customFilter, times(1)).searchByFilter(eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Count processor success")
    void countSuccess() {
        when(customFilter.countByFilter(eq(Processor.class), anyString())).thenReturn(1L);
        Long result = processorServiceImpl.count("(filter)");
        assertNotNull(result);
        assertEquals(1L, result);
        verify(customFilter, times(1)).countByFilter(eq(Processor.class), "filter");
    }

    @Test
    @DisplayName("count processor success with null filter")
    void countSuccessWithNullFilter() {
        when(customFilter.countByFilter(eq(Processor.class), isNull())).thenReturn(1L);
        Long result = processorServiceImpl.count(null);
        assertNotNull(result);
        assertEquals(1L, result);
        verify(customFilter, times(1)).countByFilter(eq(Processor.class), isNull());
    }

    @Test
    @DisplayName("Count processor failure")
    void countFailure() {
        when(customFilter.countByFilter(eq(Processor.class), anyString()))
                .thenThrow(new BusinessException("Count operation failed"));
        BusinessException exception = assertThrows(BusinessException.class,
                () -> processorServiceImpl.count("(filter)"));
        assertEquals("Count operation failed", exception.getMessage());
        verify(customFilter, times(1)).countByFilter(eq(Processor.class), "filter");
    }

}
