package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.service.TriggerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(TriggerRestImpl.class)
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc(addFilters = false)
class TriggerRestImplTest {

    @Autowired
    private MockMvc mockMvc;

   @MockitoBean
    private TriggerService service;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Search triggers successfully")
    void search() throws Exception {
        List<TriggerResponseDto> mockResponse = Collections.singletonList(new TriggerResponseDto());
        when(service.search(anyString(), any(), any(), anyString(), anyString())).thenReturn(mockResponse);

        mockMvc.perform(get("/trigger/search")
                        .param("filter", "test-filter")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "id")
                        .param("orderType", "asc")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(mockResponse)));
    }

    @Test
    @DisplayName("Count triggers successfully")
    void count() throws Exception {
        when(service.count(anyString())).thenReturn(5L);

        mockMvc.perform(get("/trigger/count")
                        .param("filter", "test-filter")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("5"));
    }

    @Test
    @DisplayName("Save trigger successfully")
    void save() throws Exception {
        TriggerRequestDto requestDto = new TriggerRequestDto();
        Map<String, String> response = Map.of("message", "Trigger saved successfully");
        when(service.createTrigger(any())).thenReturn(response);

        mockMvc.perform(post("/trigger/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));
    }

    @Test
    @DisplayName("Update trigger successfully")
    void update() throws Exception {
        TriggerRequestDto requestDto = new TriggerRequestDto();
        Map<String, String> response = Map.of("message", "Trigger updated successfully");
        when(service.updateTrigger(any())).thenReturn(response);

        mockMvc.perform(post("/trigger/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));
    }
}
