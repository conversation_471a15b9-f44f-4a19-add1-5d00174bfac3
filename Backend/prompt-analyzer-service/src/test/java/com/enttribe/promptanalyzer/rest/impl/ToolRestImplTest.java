package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tool.*;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.service.ToolService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ToolRestImpl.class)
@DisplayName("Tool Rest Implementation Tests")
@AutoConfigureMockMvc(addFilters = false)
class ToolRestImplTest {

    private static final String SOURCE_CODE = "sourceCode";
    private static final String CLASS_NAME = "className";
    private static final String STATUS = "status";
    private static final String JSON_STATUS = "$.status";
    private static final String SUCCESS = "success";

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private ToolService toolService;

    @Test
    @DisplayName("Save success")
    void saveToolSuccess() throws Exception {
        ToolDto toolDto = new ToolDto();
        when(toolService.createTool(any(ToolDto.class)))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(toolDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("create tool from swagger success")
    void createToolFromSwaggerSuccess() throws Exception {
        SwaggerDto swaggerDto = new SwaggerDto();
        when(toolService.generateTools(any(SwaggerDto.class)))
                .thenReturn(Map.of(SUCCESS, 1, "failed", 0));

        mockMvc.perform(post("/tool/toolFromSwagger")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(swaggerDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("create tool from swagger failure")
    void createToolFromSwaggerFailure() throws Exception {
        // Arrange
        SwaggerDto swaggerDto = new SwaggerDto();
        String errorMessage = "Failed to create tools from Swagger";
        when(toolService.generateTools(any(SwaggerDto.class)))
                .thenThrow(new RuntimeException(errorMessage));

        // Act & Assert
        mockMvc.perform(post("/tool/toolFromSwagger")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(swaggerDto)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath(JSON_STATUS).value("failed"))
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(toolService).generateTools(any(SwaggerDto.class));
    }

    @Test
    @DisplayName("search tools success")
    void searchSuccess() throws Exception {
        List<ToolConvertorDto> tools = List.of(new ToolConvertorDto());
        when(toolService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(tools);

        mockMvc.perform(get("/tool/search")
                        .param("filter", "test")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "name")
                        .param("orderType", "asc"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @DisplayName("count tools success")
    void countSuccess() throws Exception {
        when(toolService.count(anyString())).thenReturn(1L);

        mockMvc.perform(get("/tool/count")
                        .param("filter", "test"))
                .andExpect(status().isOk())
                .andExpect(content().string("1"));
    }

    @Test
    @DisplayName("Delete By Id success")
    void deleteByIdSuccess() throws Exception {
        when(toolService.softDelete(anyInt())).thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("id", 1))))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("get tool by id success")
    void getToolByIdSuccess() throws Exception {
        ToolConvertorDto tool = new ToolConvertorDto();
        when(toolService.getToolById(anyInt())).thenReturn(tool);

        mockMvc.perform(get("/tool/findById/{toolId}", 1))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tool)));
    }

    @Test
    @DisplayName("change tool status success")
    void changeToolStatusSuccess() throws Exception {
        Map<String, Object> request = Map.of("id", 1, STATUS, "active");
        when(toolService.changeToolStatus(anyInt(), anyString()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/changeToolStatus")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("update tool success")
    void updateSuccess() throws Exception {
        ToolDto toolDto = new ToolDto();

        when(toolService.updateTool(any(ToolDto.class)))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(toolDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("get tools by application success")
    void getToolsByApplicationSuccess() throws Exception {
        List<ToolDtoSdk> tools = List.of(new ToolDtoSdk());
        when(toolService.getToolsByApplication(anyString())).thenReturn(tools);

        mockMvc.perform(get("/tool/getToolsByApplication/{appName}", "testApp"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @DisplayName("create tool from workflow success")
    void createToolFromWorkflowSuccess() throws Exception {
        ToolWorkflowDto workflowDto = new ToolWorkflowDto();
        doNothing().when(toolService).generateToolsFromWorkflow(any(ToolWorkflowDto.class));

        mockMvc.perform(post("/tool/toolFromWorkflow")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(workflowDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("create tool from workflow failure")
    void createToolFromWorkflowFailure() throws Exception {
        // Arrange
        ToolWorkflowDto workflowDto = new ToolWorkflowDto();
        String errorMessage = "Failed to generate workflow";
        doThrow(new RuntimeException(errorMessage))
                .when(toolService)
                .generateToolsFromWorkflow(any(ToolWorkflowDto.class));

        // Act & Assert
        mockMvc.perform(post("/tool/toolFromWorkflow")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(workflowDto)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath(JSON_STATUS).value("failed"))
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(toolService).generateToolsFromWorkflow(any(ToolWorkflowDto.class));
    }

    @Test
    @DisplayName("get tool by agent id success")
    void getToolByAgentIdSuccess() throws Exception {
        List<Tool> tools = List.of(new Tool());
        when(toolService.getToolByAgentId(anyLong())).thenReturn(tools);

        mockMvc.perform(get("/tool/getToolByAgentId/{agentId}", 1))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @DisplayName("get tools by ids success")
    void getToolsByIdsSuccess() throws Exception {
        List<Integer> ids = List.of(1, 2);
        List<ToolDtoSdk> tools = List.of(new ToolDtoSdk());
        when(toolService.getToolsByIds(anyList())).thenReturn(tools);

        mockMvc.perform(post("/tool/getToolsByIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(ids)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @DisplayName("get tools by ids v1 success")
    void getToolsByIdsV1Success() throws Exception {
        List<Integer> ids = List.of(1, 2);
        List<ToolConvertorDto> tools = List.of(new ToolConvertorDto());
        when(toolService.getToolsByIdsV1(anyList())).thenReturn(tools);

        mockMvc.perform(post("/tool/v1/getToolsByIds")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(ids)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(tools)));
    }

    @Test
    @DisplayName("check compilation success")
    void checkCompilationSuccess() throws Exception {
        Map<String, String> request = Map.of(
                SOURCE_CODE , "public class TestClass{}",
                CLASS_NAME, "TestClass"
        );
        when(toolService.checkCompilation(anyString(), anyString())).thenReturn(true);

        mockMvc.perform(post("/tool/checkCompilation")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").value(true));
    }

    @Test
    @DisplayName("register agent as tool success")
    void registerAgentAsToolSuccess() throws Exception {
        when(toolService.registerAgentAsTool(anyLong()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(get("/tool/registerAgentAsTool/{id}", 1))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }

    @Test
    @DisplayName("export tool success")
    void exportToolSuccess() throws Exception {
        mockMvc.perform(get("/tool/exportTool/{appName}", "testApp")
                        .accept(MediaType.APPLICATION_OCTET_STREAM_VALUE))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("import tool success")
    void importToolSuccess() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "{\"test\": \"data\"}".getBytes()
        );

        mockMvc.perform(multipart("/tool/importTool")
                        .file(file))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("exists tool success")
    void existsToolSuccess() throws Exception {
        when(toolService.existsTool(anyString()))
                .thenReturn(Map.of("exists", true));

        mockMvc.perform(post("/tool/existsTool")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("toolName", "testTool"))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true));
    }



    @Test
    @DisplayName("update tag by id success")
    void updateTagByIdSuccess() throws Exception {
        when(toolService.updateTagById(anyInt(), any()))
                .thenReturn(Map.of(STATUS, SUCCESS));

        mockMvc.perform(post("/tool/updateTagById/{id}", 1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("tags", "tag1,tag2"))))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_STATUS).value(SUCCESS));
    }
}