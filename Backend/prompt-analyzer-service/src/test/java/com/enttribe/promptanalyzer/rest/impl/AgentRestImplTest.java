package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import com.enttribe.promptanalyzer.service.AgentService;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.security.test.context.support.WithMockUser;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

@WebMvcTest(AgentRestImpl.class)  // Loads only the web layer
class AgentRestImplTest {

    private static final String TEST_AGENT = "testAgent";
    private static final String TEST_QUERY = "test_query";

   @MockitoBean
    private AgentService agentService;

   @MockitoBean
    private CustomAgentService customAgentService;

   @MockitoBean
    private NifiFlowService nifiFlowService;

   @MockitoBean
    private AgentHistoryService agentHistoryService;

    @Autowired
    private MockMvc mockMvc;

    private AgentDto agentDto;
    private CustomAgentDto customAgentDto;
    private NifiFlowDto flowDto;
    private Map<String, String> expectedResponse;

    @BeforeEach
     void setUp() {
        agentDto = new AgentDto();
        customAgentDto = new CustomAgentDto();
        flowDto = new NifiFlowDto();
        expectedResponse = Map.of("status", "success");
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})  // Security role
    @DisplayName("Create agent successfully")
     void createAgentSuccess() throws Exception {
        when(agentService.createAgent(any(AgentDto.class))).thenReturn(expectedResponse);

        mockMvc.perform(MockMvcRequestBuilders.post("/agent/create")
                        .content(asJsonString(agentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))  // Add CSRF token for test
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("success"));

        verify(agentService).createAgent(any(AgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Search for agents")
    void searchAgents() throws Exception {
        // Arrange
        String query = TEST_QUERY;
        int page = 0;
        int size = 10;
        String sortBy = "name";
        String order = "asc";

        AgentConvertDto agentConvertDto = new AgentConvertDto();

        List<AgentConvertDto> expected = List.of(agentConvertDto);

        when(agentService.search(query, page, size, sortBy, order)).thenReturn(expected);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/agent/search")
                        .param("filter", query)
                        .param("offset", String.valueOf(page))
                        .param("size", String.valueOf(size))
                        .param("orderby", sortBy)
                        .param("orderType", order)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk());

        verify(agentService).search(query, page, size, sortBy, order);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Get plan for user query successfully")
     void getPlanForUserQuerySuccess() throws Exception {
        Map<String, String> expectedPlan = Map.of("plan", "generated");
        when(customAgentService.getPlanforUserQuery(any(CustomAgentDto.class))).thenReturn(expectedPlan);

        mockMvc.perform(MockMvcRequestBuilders.post("/agent/getPlanforUserQuery")
                        .content(asJsonString(customAgentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.plan").value("generated"));

        verify(customAgentService).getPlanforUserQuery(any(CustomAgentDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_READ"})
    @DisplayName("Get agent history successfully")
     void getAgentHistorySuccess() throws Exception {
        AgentHistory expectedHistory = new AgentHistory();
        when(agentHistoryService.getAgentHistory(TEST_AGENT)).thenReturn(expectedHistory);

        mockMvc.perform(MockMvcRequestBuilders.get("/agent/getAgentHistory")
                        .param("processGroupId", TEST_AGENT))  // Correct way to send query param
                .andExpect(MockMvcResultMatchers.status().isOk());

        verify(agentHistoryService).getAgentHistory(TEST_AGENT);
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger in NiFi successfully")
     void createTriggerInNifiSuccess() throws Exception {
        Map<String, String> expectedNifiResponse = Map.of("status", "triggered");
        when(nifiFlowService.createTriggerNifi(any(NifiFlowDto.class))).thenReturn(expectedNifiResponse);

        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerInNifi")
                        .content(asJsonString(flowDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("triggered"));

        verify(nifiFlowService).createTriggerNifi(any(NifiFlowDto.class));
    }

    @Test
    @WithMockUser(username = "testUser", roles = {"API_AGENT_WRITE"})
    @DisplayName("Create trigger name successfully")
     void createTriggerNameSuccess() throws Exception {
        Map<String, String> requestBody = Map.of("userQuery", TEST_QUERY);
        Map<String, String> expectedTriggerNameResponse = Map.of("triggerName", "generatedName");

        when(customAgentService.createTriggerNameDescription(TEST_QUERY)).thenReturn(expectedTriggerNameResponse);

        mockMvc.perform(MockMvcRequestBuilders.post("/agent/createTriggerName")
                        .content(asJsonString(requestBody))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.triggerName").value("generatedName"));

        verify(customAgentService).createTriggerNameDescription(TEST_QUERY);
    }

     static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new BusinessException("Failed to convert object to JSON", e);
        }
    }
}
