package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.service.ProcessorService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Map;

import static java.util.Collections.singletonList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(ProcessorRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class ProcessorRestImplTest {

    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_ERROR = "error";
    private static final String MESSAGE_SUCCESS = "Operation completed successfully";
    private static final String MESSAGE_ERROR = "Invalid request parameters";
    private static final String FILTER_CATEGORY_TEST = "category:test";
    private static final String JSON_PATH_STATUS = "$.status";
    private static final String JSON_PATH_MESSAGE = "$.message";
    
    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private ProcessorService processorService;

    private ProcessorRequestDto requestDto;
    private ProcessorResponseDto responseDto;
    private Map<String, Object> successResponse;

    @BeforeEach
    void setUp() {
        requestDto = createProcessorRequest();
        responseDto = createProcessorResponse();
        successResponse = Map.of("status", STATUS_SUCCESS, "message", MESSAGE_SUCCESS);
    }

    @Test
    @DisplayName("Save Processor - Success")
    void saveProcessorSuccess() throws Exception {
        when(processorService.save(any(ProcessorRequestDto.class))).thenReturn(successResponse);
        mockMvc.perform(post("/processor/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_SUCCESS))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_SUCCESS))
                .andDo(print());
        verify(processorService).save(any(ProcessorRequestDto.class));
    }

    @Test
    @DisplayName("Update Processor - Success")
    void updateProcessorSuccess() throws Exception {
        Integer id = 1;
        when(processorService.update(eq(id), any(ProcessorRequestDto.class))).thenReturn(successResponse);
        mockMvc.perform(post("/processor/update/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_SUCCESS))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_SUCCESS))
                .andDo(print());
        verify(processorService).update(eq(id), any(ProcessorRequestDto.class));
    }

    @Test
    @DisplayName("Search Processors - Success")
    void searchProcessorsSuccess() throws Exception {
        when(processorService.search(FILTER_CATEGORY_TEST, 0, 10, "displayName", "asc"))
                .thenReturn(singletonList(responseDto));
        mockMvc.perform(get("/processor/search")
                        .param("filter", FILTER_CATEGORY_TEST)
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "displayName")
                        .param("orderType", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andDo(print());
        verify(processorService).search(FILTER_CATEGORY_TEST, 0, 10, "displayName", "asc");
    }

    @Test
    @DisplayName("Count Processors - Success")
    void countProcessorsSuccess() throws Exception {
        when(processorService.count(FILTER_CATEGORY_TEST)).thenReturn(5L);
        mockMvc.perform(get("/processor/count").param("filter", FILTER_CATEGORY_TEST))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(5))
                .andDo(print());
        verify(processorService).count(FILTER_CATEGORY_TEST);
    }

    @Test
    @DisplayName("Save Processor - Invalid Request")
    void saveProcessorInvalidRequest() throws Exception {
        Map<String, Object> errorResponse = Map.of("status", STATUS_ERROR, "message", MESSAGE_ERROR);
        when(processorService.save(any(ProcessorRequestDto.class))).thenReturn(errorResponse);
        mockMvc.perform(post("/processor/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(new ProcessorRequestDto())))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_ERROR))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_ERROR))
                .andDo(print());
    }

    private ProcessorRequestDto createProcessorRequest() {
        ProcessorRequestDto dto = new ProcessorRequestDto();
        dto.setKey("test-key");
        dto.setDisplayName("Test Processor");
        dto.setIcon("test-icon.png");
        dto.setStyleType("default");
        dto.setCategory("test-category");
        dto.setSubCategory("test-subcategory");
        return dto;
    }

    private ProcessorResponseDto createProcessorResponse() {
        return ProcessorResponseDto.builder()
                .id(1)
                .key("test-key")
                .displayName("Test Processor")
                .icon("test-icon.png")
                .styleType("default")
                .category("test-category")
                .subCategory("test-subcategory")
                .jsonStructure("{}")
                .build();
    }
}
