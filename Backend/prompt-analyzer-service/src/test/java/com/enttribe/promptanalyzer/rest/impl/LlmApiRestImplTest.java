package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.ai.dto.MessageDto;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(LlmApiRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class LlmApiRestImplTest {

    private static final String STATUS_SUCCESS = "success";
    private static final String PROMPT_ID = "promptId";
    private static final String TEST_PROMPT_1 = "test-prompt-1";
    private static final String JSON_PATH_RESULT = "$.result";
    private static final String GENERAL_TYPE = "general";

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private LlmApiService llmApiService;

    private ChatCompletionRequestDto chatCompletionRequestDto;

    @BeforeEach
    void setUp() {
        chatCompletionRequestDto = new ChatCompletionRequestDto();
        chatCompletionRequestDto.setMaxTokens(2000);
        chatCompletionRequestDto.setModel("gpt-4");
        chatCompletionRequestDto.setTemperature(0.7);
        chatCompletionRequestDto.setTopP(1.0);
        chatCompletionRequestDto.setJsonMode(false);
        chatCompletionRequestDto.setProvider("openai");

        chatCompletionRequestDto.setMessages(List.of(
                new MessageDto("system", "You are a helpful assistant."),
                new MessageDto("user", "Hello, how are you?")
        ));
    }

    @Test
    @DisplayName("Chat completion success")
    void chatCompletionSuccess() throws Exception {
        when(llmApiService.chatCompletion(any(ChatCompletionRequestDto.class)))
                .thenReturn(Map.of("response", "I'm doing well, thank you for asking!", "status", STATUS_SUCCESS));

        mockMvc.perform(post("/chat/completions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(chatCompletionRequestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.response").value("I'm doing well, thank you for asking!"))
                .andExpect(jsonPath("$.status").value(STATUS_SUCCESS))
                .andDo(print());
        verify(llmApiService).chatCompletion(any(ChatCompletionRequestDto.class));
    }

    @Test
    @DisplayName("Execute prompt success with format=true")
    void executePromptWithFormatSuccess() throws Exception {
        when(llmApiService.executePrompt(anyString(), any(Map.class), anyBoolean()))
                .thenReturn(Map.of("result", "This is the formatted response", "status", STATUS_SUCCESS, PROMPT_ID, TEST_PROMPT_1));

        mockMvc.perform(post("/chat/executePrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of(
                                PROMPT_ID, TEST_PROMPT_1,
                                "variableMap", Map.of("key", "value"),
                                "format", true))))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_RESULT).value("This is the formatted response"))
                .andExpect(jsonPath("$.status").value(STATUS_SUCCESS))
                .andExpect(jsonPath("$.promptId").value(TEST_PROMPT_1))
                .andDo(print());

        verify(llmApiService).executePrompt(anyString(), any(Map.class), anyBoolean());
    }

    @Test
    @DisplayName("Execute prompt with format=true success")
    void executePromptWithFormatTrueSuccess() throws Exception {
        Map<String, Object> executePromptRequest = new HashMap<>();
        executePromptRequest.put("promptId", "test-prompt-1");
        executePromptRequest.put("variableMap", Map.of("variable1", "value1"));
        executePromptRequest.put("format", true);

        Map<String, Object> expectedResponse = Map.of("result", "This is the formatted response", "status", "success", "promptId", "test-prompt-1");
        when(llmApiService.executePrompt(anyString(), any(Map.class), eq(true)))
                .thenReturn(expectedResponse);

        mockMvc.perform(post("/chat/executePrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(executePromptRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").value("This is the formatted response"))
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.promptId").value("test-prompt-1"))
                .andDo(print());
        verify(llmApiService).executePrompt(anyString(), any(Map.class), eq(true));
    }

    @Test
    @DisplayName("Execute prompt with null format success")
    void executePromptWithNullFormatSuccess() throws Exception {
        Map<String, Object> executePromptRequest = new HashMap<>();
        executePromptRequest.put("promptId", "test-prompt-1");
        executePromptRequest.put("variableMap", Map.of("variable1", "value1"));
        Map<String, Object> expectedResponse = Map.of("result", "This is the unformatted response", "status", "success", "promptId", "test-prompt-1");
        when(llmApiService.executePrompt(anyString(), any(Map.class), eq(false)))
                .thenReturn(expectedResponse);
        mockMvc.perform(post("/chat/executePrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(executePromptRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").value("This is the unformatted response"))
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.promptId").value("test-prompt-1"))
                .andDo(print());
        verify(llmApiService).executePrompt(anyString(), any(Map.class), eq(false));
    }

    @Test
    @DisplayName("Execute prompt V1 success")
    void executePromptV1Success() throws Exception {
        when(llmApiService.executePromptV1(anyString(), any(Map.class)))
                .thenReturn("This is the V1 response");

        mockMvc.perform(post("/chat/v1/executePrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of(
                                PROMPT_ID, TEST_PROMPT_1,
                                "variableMap", Map.of("key", "value")))))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_RESULT).value("This is the V1 response"))
                .andDo(print());

        verify(llmApiService).executePromptV1(anyString(), any(Map.class));
    }

    @Test
    @DisplayName("Generate fresh system prompt success")
    void generateFreshSystemPromptSuccess() throws Exception {
        when(llmApiService.generateFreshSystemPrompt("Create a greeting message", GENERAL_TYPE))
                .thenReturn("You are a helpful assistant that creates professional greetings.");

        mockMvc.perform(post("/chat/generateSystemPrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of(
                                "userInput", "Create a greeting message",
                                "type", GENERAL_TYPE))))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_RESULT).value("You are a helpful assistant that creates professional greetings."))
                .andDo(print());

        verify(llmApiService).generateFreshSystemPrompt("Create a greeting message", GENERAL_TYPE);
    }

    @Test
    @DisplayName("Generate improved system prompt success")
    void generateImprovedSystemPromptSuccess() throws Exception {
        when(llmApiService.improveSystemPrompt("Make it more formal", "Hello there!", GENERAL_TYPE))
                .thenReturn("You are a helpful assistant that creates formal and professional greetings.");

        mockMvc.perform(post("/chat/generateSystemPrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of(
                                "userInput", "Make it more formal",
                                "oldPrompt", "Hello there!",
                                "type", GENERAL_TYPE))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.result").value("You are a helpful assistant that creates formal and professional greetings."))
                .andDo(print());
        verify(llmApiService).improveSystemPrompt("Make it more formal", "Hello there!", "general");
    }

}