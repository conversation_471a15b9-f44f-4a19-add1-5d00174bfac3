package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.service.LlmModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(LlmModelRestImpl.class)
@DisplayName("LLM Model Rest Implementation Tests")
@AutoConfigureMockMvc(addFilters = false)
class LlmModelRestImplTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private LlmModelService llmService;

    @Test
    @DisplayName("create llm model success")
    void createSuccess() throws Exception {
        // Arrange
        LlmModel llmModel = new LlmModel();
        llmModel.setProvider("test-provider1");

        when(llmService.create(any(LlmModel.class)))
                .thenReturn(Map.of("status", "success"));

        // Act & Assert
        mockMvc.perform(post("/llm-model/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(llmModel)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"));

        verify(llmService).create(any(LlmModel.class));
    }

    @Test

    @DisplayName("get models by provider success")
    void getModelsByProviderSuccess() throws Exception {
        // Arrange
        ProviderModelDto providerModelDto = new ProviderModelDto();
        providerModelDto.setProvider("test-provider");

        when(llmService.getModelsByProvider(anyString()))
                .thenReturn(providerModelDto);

        // Act & Assert
        mockMvc.perform(get("/llm-model/getModelsByProvider")
                        .param("provider", "test-provider"))  // Ensure the provider is passed as a request param
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(providerModelDto)));

        verify(llmService).getModelsByProvider(anyString()); // Ensure exact match
    }


    @Test
    @DisplayName("get all providers with models success")
    void getAllProvidersWithModelsSuccess() throws Exception {
        // Arrange
        List<ProviderModelDto> providers = List.of(new ProviderModelDto());
        when(llmService.getAllProvidersWithModels())
                .thenReturn(providers);

        // Act & Assert
        mockMvc.perform(get("/llm-model/all"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(providers)));

        verify(llmService).getAllProvidersWithModels();
    }

    @Test
    @DisplayName("get llm models for SDK success")
    void getLlmModelsForSDKSuccess() throws Exception {
        // Arrange
        List<LlmModelSdkDto> sdkModels = List.of(new LlmModelSdkDto());
        when(llmService.getLlmModelsForSDK(anyString(), anyString()))
                .thenReturn(sdkModels);

        // Act & Assert
        mockMvc.perform(get("/llm-model/getLlmModelsForSDK/test-app", "test-app"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(sdkModels)));

        verify(llmService).getLlmModelsForSDK("test-app", "chat");
    }

    @Test
    @DisplayName("get llm models by type for SDK success")
    void getLlmModelsByTypeForSDKSuccess() throws Exception {
        // Arrange
        List<LlmModelSdkDto> typeModels = List.of(new LlmModelSdkDto());
        when(llmService.getLlmModelsByTypeForSDK(anyString()))
                .thenReturn(typeModels);

        // Act & Assert
        mockMvc.perform(get("/llm-model/getLlmModelsByType/type/test-type")
                        .param("test-type", "test-type"))  // Pass 'type' as a query parameter
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(typeModels)));

        verify(llmService).getLlmModelsByTypeForSDK(anyString()); // Ensure exact match
    }
}