package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.TriggerDao;
import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Trigger;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Date;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@SpringBootTest

class TriggerServiceImplTest {

   @MockitoBean
    private TriggerDao triggerDao;

   @MockitoBean
    private CustomFilter customFilter;

    @Autowired
    private TriggerServiceImpl triggerService;

    private TriggerRequestDto triggerRequestDto;
    private Trigger trigger;

    @BeforeEach
    void setUp() {
        triggerRequestDto = new TriggerRequestDto();
        triggerRequestDto.setId(1);
        triggerRequestDto.setName("Test");
        triggerRequestDto.setDescription("Test Description");
        triggerRequestDto.setDisplayName("Test Display Name");
        triggerRequestDto.setMetadata("Test Metadata");

        trigger = new Trigger();
        trigger.setId(1);
        trigger.setName("Test");
        trigger.setDescription("Test Description");
        trigger.setDisplayName("Test Display Name");
        trigger.setMetadata("Test Metadata");
        trigger.setCreatedTime(new Date());
    }

    @Test
    @DisplayName("Create trigger successfully")
    void createTriggerSuccess() {
        when(triggerDao.save(any(Trigger.class))).thenReturn(trigger);
        Map<String, String> response = triggerService.createTrigger(triggerRequestDto);
        assertNotNull(response, "Response should not be null");
        assertEquals("success", response.get("status"), "Status should be 'success'");
        verify(triggerDao, times(1)).save(any(Trigger.class));
    }

    @Test
    @DisplayName("Trigger creation failure")
    void createTriggerFailure() {
        doThrow(new RuntimeException("Database error")).when(triggerDao).save(any(Trigger.class));
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            triggerService.createTrigger(triggerRequestDto);
        });
        assertEquals("Unable to save Trigger: Database error", exception.getMessage(), "Exception message should match");
        verify(triggerDao, times(1)).save(any(Trigger.class));
    }

    @Test
    @DisplayName("Search trigger successfully")
    void searchTriggerSuccess() {
        when(customFilter.searchByFilter(eq(Trigger.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList(trigger));

        List<TriggerResponseDto> result = triggerService.search("name='Test'", 0, 10, "id", "asc");

        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Result size should be 1");
        verify(customFilter, times(1)).searchByFilter(eq(Trigger.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Empty list when no triggers are found")
    void searchTriggerFailure() {
        when(customFilter.searchByFilter(eq(Trigger.class), nullable(String.class), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());

        List<TriggerResponseDto> result = triggerService.search(null, 0, 10, "id", "asc");

        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(customFilter, times(1)).searchByFilter(eq(Trigger.class), nullable(String.class), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("Count triggers successfully")
    void countTriggerSuccess() {
        when(customFilter.countByFilter(eq(Trigger.class), anyString())).thenReturn(5L);
        Long count = triggerService.count("name='Test'");
        assertNotNull(count, "Count should not be null");
        assertEquals(5L, count, "Count should be 5");
        verify(customFilter, times(1)).countByFilter(eq(Trigger.class), anyString());
    }

    @Test
    @DisplayName("Update trigger successfully")
    void updateTriggerSuccess() {
        when(triggerDao.findById(triggerRequestDto.getId())).thenReturn(Optional.of(trigger));
        when(triggerDao.save(any(Trigger.class))).thenReturn(trigger);
        var response = triggerService.updateTrigger(triggerRequestDto);
        assertNotNull(response, "Response should not be null");
        assertEquals("success", response.get("status"), "Status should be 'success'");
        verify(triggerDao, times(1)).save(any(Trigger.class));
    }

    @Test
    @DisplayName("Updating non-existent trigger failure case")
    void updateTriggerFailure() {
        when(triggerDao.findById(triggerRequestDto.getId())).thenReturn(Optional.empty());

        BusinessException exception = assertThrows(BusinessException.class,
                () -> triggerService.updateTrigger(triggerRequestDto));

        assertEquals("Trigger Id :1 not found", exception.getMessage(), "Exception message should match");
    }
}
