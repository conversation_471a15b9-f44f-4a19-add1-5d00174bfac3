package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.*;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.PromptService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import com.fasterxml.jackson.databind.ObjectMapper;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.containsString;

@WebMvcTest(PromptRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class PromptRestImplTest {

    @Autowired
    private MockMvc mockMvc;

   @MockitoBean
    private PromptService promptService;

    @Autowired
    private ObjectMapper objectMapper;

    private PromptDto basePromptDto;
    private PromptRequestDto basePromptRequestDto;

    @BeforeEach
    void setUp() {
        basePromptDto = new PromptDto();
        basePromptDto.setApplication("testApplication");
        basePromptDto.setPromptId("test-prompt-id");
        basePromptDto.setTemperature(1.0);
        basePromptDto.setMaxTokens(1000);
        basePromptDto.setTopP(0.7);
        basePromptDto.setName("Test Prompt");

        basePromptRequestDto = new PromptRequestDto();
        basePromptRequestDto.setApplication("testApplication");
        basePromptRequestDto.setName("Test Prompt");
        basePromptRequestDto.setCategory("testCategory");
        basePromptRequestDto.setStatus("active");
    }

    @Test
    @DisplayName("Health check endpoint success")
    void success() throws Exception {
        mockMvc.perform(get("/prompt/ping"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"success\"}"))
                .andDo(print());
    }

    @Test
    @DisplayName("Save prompt success")
    void promptSave() throws Exception {
        when(promptService.savePrompt(any(PromptDto.class))).thenReturn(Map.of("status", "saved"));
        mockMvc.perform(post("/prompt/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto)))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"saved\"}"))
                .andDo(print());
    }

    @Test
    @DisplayName("Get versions of prompt success")
    void getVersionsOfPrompt() throws Exception {
        List<PromptVersionDetailsDto> versions = List.of(
                new PromptVersionDetailsDto(1, "1.0"),
                new PromptVersionDetailsDto(2, "2.0")
        );
        when(promptService.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString())).thenReturn(versions);
        mockMvc.perform(post("/prompt/getVersionsOfPrompt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].version").value("1.0"))
                .andDo(print());
    }

    @Test
    @DisplayName("Edit prompt success")
    void promptEdit() throws Exception {
        Map<String, String> response = Map.of("status", "updated");
        when(promptService.updatePrompt(any(PromptDto.class))).thenReturn(response);
        mockMvc.perform(post("/prompt/edit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptDto)))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"updated\"}"))
                .andDo(print());
    }

    @Test
    @DisplayName("Delete prompt by ID success")
    void deleteById() throws Exception {
        Map<String, Integer> request = Map.of("id", 1);
        Map<String, String> response = Map.of("status", "deleted");

        when(promptService.softDelete(1)).thenReturn(response);
        mockMvc.perform(post("/prompt/deleteById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"deleted\"}"))
                .andDo(print());
    }

    @Test
    @DisplayName("Get prompt basic details by application success")
    void getPromptBasicDetailByApplication() throws Exception {
        Map<String, String> request = Map.of("application", "testApp");
        List<Map<String, String>> response = List.of(
                Map.of("name", "Test Prompt", "status", "active")
        );

        when(promptService.getPromptBasicDetailByApplication("testApp")).thenReturn(response);
        mockMvc.perform(post("/prompt/getPromptBasicDetailByApplication")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name").value("Test Prompt"))
                .andDo(print());
    }

    @Test
    @DisplayName("Get prompt by ID success")
    void getPromptById() throws Exception {
        Map<String, Integer> request = Map.of("id", 1);
        PromptConvertorDto response = PromptConvertorDto.builder()
                .id(1)
                .name("Test Prompt")
                .build();

        when(promptService.getPromptById(1)).thenReturn(response);
        mockMvc.perform(post("/prompt/viewById")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Test Prompt"))
                .andDo(print());
    }

    @Test
    @DisplayName("Get SDK prompt by ID success")
    void getPromptByIdSdk() throws Exception {
        PromptDtoSdk response = PromptDtoSdk.builder()
                .id("1")
                .promptName("Test Prompt")
                .build();

        when(promptService.findPromptById(1)).thenReturn(response);

        mockMvc.perform(get("/prompt/getPromptById/{id}", 1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("1"))
                .andExpect(jsonPath("$.promptName").value("Test Prompt"))
                .andDo(print());
    }

    @Test
    @DisplayName("Check prompt exists success")
    void exists() throws Exception {
        Map<String, Object> response = Map.of("exists", true);
        when(promptService.exists(any(PromptRequestDto.class))).thenReturn(response);
        mockMvc.perform(post("/prompt/exists")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(basePromptRequestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true))
                .andDo(print());
    }

    @Test
    @DisplayName("Get distinct applications success")
    void getDistinctApplications() throws Exception {
        List<String> response = List.of("app1", "app2");
        when(promptService.getDistinctApplications(anyString())).thenReturn(response);
        mockMvc.perform(get("/prompt/getDistinctApplications")
                        .param("applicationName", "test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0]").value("app1"))
                .andDo(print());
    }

    @Test
    @DisplayName("Get distinct categories by app success")
    void getDistinctCategoriesByAppSuccess() throws Exception {
        String applicationName = "testApp";
        List<String> expectedCategories = List.of(
                "category1",
                "category2",
                "category3"
        );

        when(promptService.getDistinctCategoriesByApp(applicationName))
                .thenReturn(expectedCategories);
        mockMvc.perform(get("/prompt/getDistinctCategoriesByApp")
                        .param("applicationName", applicationName)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$[0]").value("category1"))
                .andExpect(jsonPath("$[1]").value("category2"))
                .andExpect(jsonPath("$[2]").value("category3"))
                .andDo(print());
        verify(promptService, times(1)).getDistinctCategoriesByApp(applicationName);
    }

    @Test
    @DisplayName("Filter prompts success")
    void filterSuccess() throws Exception {
        Map<String, Object> filterMap = Map.of(
                "application", "testApp",
                "category", "testCategory"
        );

        Message message = new Message();
        message.setRole("system");
        message.setContent("Test message");

        Prompt prompt1 = new Prompt();
        prompt1.setId(1);
        prompt1.setApplication("testApp");
        prompt1.setName("Test Prompt 1");
        prompt1.setCategory("testCategory");
        prompt1.setMessages(List.of(message));

        Prompt prompt2 = new Prompt();
        prompt2.setId(2);
        prompt2.setApplication("testApp");
        prompt2.setName("Test Prompt 2");
        prompt2.setCategory("testCategory");
        prompt2.setMessages(List.of(message));

        List<Prompt> promptList = List.of(prompt1, prompt2);
        when(promptService.filter(any(Map.class))).thenReturn(promptList);

        mockMvc.perform(post("/prompt/filter")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(filterMap)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].application").value("testApp"))
                .andExpect(jsonPath("$[0].name").value("Test Prompt 1"))
                .andExpect(jsonPath("$[0].category").value("testCategory"))
                .andExpect(jsonPath("$[0].prompt").value("system : Test message"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].name").value("Test Prompt 2"))
                .andDo(print());

        verify(promptService, times(1)).filter(any(Map.class));
    }

    @Test
    @DisplayName("Update assertion template success")
    void updateAssertionTemplateSuccess() throws Exception {
        AssertionTemplateDto assertionTemplateDto = new AssertionTemplateDto();
        assertionTemplateDto.setPromptId(1);
        assertionTemplateDto.setAssertionTemplate("test assertion template");

        Map<String, String> expectedResponse = Map.of("status", "updated");
        when(promptService.updateAssertionTemplate(any(AssertionTemplateDto.class)))
                .thenReturn(expectedResponse);

        mockMvc.perform(post("/prompt/updateAssertionTemplate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(assertionTemplateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("updated"))
                .andDo(print());

        verify(promptService, times(1)).updateAssertionTemplate(any(AssertionTemplateDto.class));
    }

    @Test
    @DisplayName("Search prompts success")
    void search() throws Exception {
        List<PromptConvertorDto> response = List.of(
                PromptConvertorDto.builder()
                        .id(1)
                        .name("Test Prompt")
                        .build()
        );

        when(promptService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(response);

        mockMvc.perform(get("/prompt/search")
                        .param("filter", "test")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "name")
                        .param("orderType", "asc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andDo(print());
    }



    @Test
    @DisplayName("Count prompts success")
    void count() throws Exception {
        when(promptService.count(anyString())).thenReturn(5L);

        mockMvc.perform(get("/prompt/count")
                        .param("filter", "test"))
                .andExpect(status().isOk())
                .andExpect(content().string("5"))
                .andDo(print());
    }

    @Test
    @DisplayName("Export CSV success")
    void promptExportCSV() throws Exception {
        Resource mockResource = new ByteArrayResource("test data".getBytes());
        ResponseEntity<Resource> response = ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=prompts.csv")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(mockResource);

        when(promptService.exportPrompt(anyString())).thenReturn(response);

        mockMvc.perform(get("/prompt/exportPrompt/{appName}", "testApp"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Disposition", containsString("attachment")))
                .andExpect(content().contentType("text/csv"))
                .andDo(print());
    }

    @Test
    @DisplayName("Import prompt success")
    void importPrompt() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.csv",
                "text/csv",
                "test data".getBytes()
        );

        Resource mockResource = new ByteArrayResource("Import successful".getBytes());
        ResponseEntity<Resource> response = ResponseEntity.ok().body(mockResource);

        when(promptService.importPrompt(any())).thenReturn(response);

        mockMvc.perform(multipart("/prompt/importPrompt")
                        .file(file))
                .andExpect(status().isOk())
                .andDo(print());
    }

    @Test
    @DisplayName("Update tag by ID success")
    void updateTagById() throws Exception {
        Map<String, String> tags = Map.of("tag1", "value1");
        Map<String, String> response = Map.of("status", "updated");

        when(promptService.updateTagById(anyInt(), anyMap())).thenReturn(response);

        mockMvc.perform(post("/prompt/updateTagById/{id}", 1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(tags)))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"status\":\"updated\"}"))
                .andDo(print());
    }

    @Test
    @DisplayName("Search V1 prompts success")
    void searchV1Success() throws Exception {
        Message message = new Message();
        message.setRole("system");
        message.setContent("Test message");

        Prompt prompt1 = new Prompt();
        prompt1.setId(1);
        prompt1.setName("Test Prompt 1");
        prompt1.setApplication("testApp");
        prompt1.setCategory("testCategory");
        prompt1.setMessages(List.of(message));

        Prompt prompt2 = new Prompt();
        prompt2.setId(2);
        prompt2.setName("Test Prompt 2");
        prompt2.setApplication("testApp");
        prompt2.setCategory("testCategory");
        prompt2.setMessages(List.of(message));

        List<Prompt> promptList = List.of(prompt1, prompt2);
        when(promptService.searchV1(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(promptList);
        mockMvc.perform(get("/prompt/v1/search")
                        .param("filter", "test")  // changed from searchText
                        .param("offset", "0")     // changed from page
                        .param("size", "10")
                        .param("orderBy", "name") // changed from sortBy
                        .param("orderType", "asc")// changed from sortDir
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Prompt 1"))
                .andExpect(jsonPath("$[0].application").value("testApp"))
                .andExpect(jsonPath("$[0].category").value("testCategory"))
                .andExpect(jsonPath("$[0].prompt").value("system : Test message"))
                .andExpect(jsonPath("$[1].id").value(2))
                .andExpect(jsonPath("$[1].name").value("Test Prompt 2"))
                .andDo(print());

        verify(promptService).searchV1(anyString(), anyInt(), anyInt(), anyString(), anyString());
    }

    @Test
    @DisplayName("Get prompts by application success")
    void getPromptByApplicationSuccess() throws Exception {
        String appName = "testApp";
        List<PromptDtoSdk> expectedPrompts = List.of(
                PromptDtoSdk.builder()
                        .id("1")
                        .promptName("Test Prompt 1")
                        .category("testCategory")
                        .promptId("test-prompt-1")
                        .model("gpt-4")
                        .build(),
                PromptDtoSdk.builder()
                        .id("2")
                        .promptName("Test Prompt 2")
                        .category("testCategory")
                        .promptId("test-prompt-2")
                        .model("gpt-4")
                        .build()
        );
        when(promptService.getPromptByApplication(appName)).thenReturn(expectedPrompts);
        mockMvc.perform(get("/prompt/getPromptByApplication/{appName}", appName)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].id").value("1"))
                .andExpect(jsonPath("$[0].promptName").value("Test Prompt 1"))
                .andExpect(jsonPath("$[0].category").value("testCategory"))
                .andExpect(jsonPath("$[0].promptId").value("test-prompt-1"))
                .andExpect(jsonPath("$[0].model").value("gpt-4"))
                .andExpect(jsonPath("$[1].id").value("2"))
                .andExpect(jsonPath("$[1].promptName").value("Test Prompt 2"))
                .andDo(print());

        verify(promptService).getPromptByApplication(appName);
    }
}
