package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.service.TagService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;

@ExtendWith(SpringExtension.class)
@WebMvcTest(TagRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class TagRestImplTest {

    @Autowired
    private MockMvc mockMvc;

   @MockitoBean
    private TagService tagService;

    @Test
    @DisplayName("Search tags success")
    void searchTagsSuccess() throws Exception {
        List<TagResponseDto> responseDtos = Collections.singletonList(new TagResponseDto());
        when(tagService.search(anyString(), anyInt(), anyInt(), anyString(), anyString())).thenReturn(responseDtos);

        mockMvc.perform(get("/tag/search")
                        .param("filter", "sample")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "id")
                        .param("orderType", "asc"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Count tags success")
    void countTagsSuccess() throws Exception {
        when(tagService.count(anyString())).thenReturn(10L);

        mockMvc.perform(get("/tag/count")
                        .param("filter", "sample"))
                .andExpect(status().isOk())
                .andExpect(content().string("10"));
    }

    @Test
    @DisplayName("Save tag success")
    void saveTagSuccess() throws Exception {
        Map<String, String> response = Map.of("result", "Tag saved successfully");

        when(tagService.save(any(TagRequestDto.class))).thenReturn(response);

        mockMvc.perform(post("/tag/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"result\":\"Tag saved successfully\"}"));
    }

    @Test
    @DisplayName("Delete tag success")
    void deleteTagSuccess() throws Exception {
        Map<String, String> response = Map.of("message", "Tag deleted successfully");

        when(tagService.softDelete(anyInt())).thenReturn(response);

        mockMvc.perform(post("/tag/deleteById/1"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"message\":\"Tag deleted successfully\"}"));
    }

    @Test
    @DisplayName("Update tag success")
    void updateTagByIdSuccess() throws Exception {
        Map<String, String> response = Map.of("message", "Tag updated successfully");

        when(tagService.update(any(TagRequestDto.class))).thenReturn(response);

        mockMvc.perform(post("/tag/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"message\":\"Tag updated successfully\"}"));
    }
}
