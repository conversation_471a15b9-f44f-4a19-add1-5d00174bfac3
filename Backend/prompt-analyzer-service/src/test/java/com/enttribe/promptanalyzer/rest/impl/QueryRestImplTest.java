package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.query.QueryRequestDto;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.service.QueryService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.hamcrest.Matchers.hasSize;

@WebMvcTest(QueryRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class QueryRestImplTest {

    private static final String STATUS_SUCCESS = "success";
    private static final String MESSAGE_SUCCESS = "Operation completed successfully";
    private static final String FILTER_TYPE_GENERAL = "type:general";
    private static final String JSON_PATH_STATUS = "$.status";
    private static final String JSON_PATH_MESSAGE = "$.message";
    private static final String TEST_USER_ID = "test-user-123";
    private static final String TEST_QUESTION = "What is AI?";
    private static final String ORDER_BY_CREATED_AT = "createdAt";
    private static final String TYPE_GENERAL = "general";

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private QueryService queryService;

    private QueryRequestDto queryRequestDto;
    private List<QueryResponseDto> queryResponseList;
    private Map<String, String> successResponse;

    @BeforeEach
    void setUp() {
        queryRequestDto = new QueryRequestDto();
        queryRequestDto.setUserId(TEST_USER_ID);
        queryRequestDto.setType(TYPE_GENERAL);
        queryRequestDto.setQuestion(TEST_QUESTION);

        QueryResponseDto response1 = createQueryResponse(1, TEST_USER_ID, TYPE_GENERAL, TEST_QUESTION);
        QueryResponseDto response2 = createQueryResponse(2, TEST_USER_ID, "technical", "How does ML work?");

        queryResponseList = List.of(response1, response2);
        successResponse = Map.of("status", STATUS_SUCCESS, "message", MESSAGE_SUCCESS);
    }

    private QueryResponseDto createQueryResponse(int id, String userId, String type, String question) {
        QueryResponseDto response = new QueryResponseDto();
        response.setId(id);
        response.setUserId(userId);
        response.setType(type);
        response.setQuestion(question);
        return response;
    }

    @Test
    @DisplayName("Test save query success")
    void saveQuerySuccess() throws Exception {
        when(queryService.createQuery(any(QueryRequestDto.class))).thenReturn(successResponse);

        mockMvc.perform(post("/query/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryRequestDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath(JSON_PATH_STATUS).value(STATUS_SUCCESS))
                .andExpect(jsonPath(JSON_PATH_MESSAGE).value(MESSAGE_SUCCESS))
                .andDo(print());

        verify(queryService).createQuery(any(QueryRequestDto.class));
    }

    @Test
    @DisplayName("Test search queries success")
    void searchQueriesSuccess() throws Exception {
        when(queryService.search(FILTER_TYPE_GENERAL, 0, 10, ORDER_BY_CREATED_AT, "desc")).thenReturn(queryResponseList);

        mockMvc.perform(get("/query/search")
                        .param("filter", FILTER_TYPE_GENERAL)
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", ORDER_BY_CREATED_AT)
                        .param("orderType", "desc"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(hasSize(2)))
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$[0].type").value(TYPE_GENERAL))
                .andExpect(jsonPath("$[0].question").value(TEST_QUESTION))
                .andDo(print());
    }

    @Test
    @DisplayName("Test count queries success")
    void countSuccess() throws Exception {
        String filter = "type:general";
        Long expectedCount = 5L;
        when(queryService.count(filter)).thenReturn(expectedCount);
        mockMvc.perform(get("/query/count")
                        .param("filter", filter))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(5))
                .andDo(print());

        verify(queryService).count(filter);
    }

    @Test
    @DisplayName("Test delete query by ID success")
    void deleteByIdSuccess() throws Exception {
        Integer id = 1;
        when(queryService.softDelete(id)).thenReturn(successResponse);
        mockMvc.perform(post("/query/deleteById/{id}", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("success"))
                .andExpect(jsonPath("$.message").value("Operation completed successfully"))
                .andDo(print());
        verify(queryService).softDelete(id);
    }

    @Test
    @DisplayName("Test save query with invalid request")
    void saveInvalidRequest() throws Exception {
        QueryRequestDto invalidDto = new QueryRequestDto();
        Map<String, String> errorResponse = Map.of(
                "status", "error",
                "message", "Invalid request parameters"
        );
        when(queryService.createQuery(any(QueryRequestDto.class))).thenReturn(errorResponse);
        mockMvc.perform(post("/query/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("error"))
                .andExpect(jsonPath("$.message").value("Invalid request parameters"))
                .andDo(print());
    }
}