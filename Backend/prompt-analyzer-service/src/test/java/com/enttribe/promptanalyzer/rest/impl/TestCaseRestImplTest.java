package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(TestCaseRestImpl.class)
@AutoConfigureMockMvc(addFilters = false)
class TestCaseRestImplTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

   @MockitoBean
    private TestCaseService testCaseService;

    @Test
    @DisplayName("Create test case success")
    void createTestCaseSuccess() throws Exception {
        TestCaseRequestDto requestDto = new TestCaseRequestDto();
        Map<String, String> response = Map.of("result", "Test case created successfully");

        Mockito.when(testCaseService.create(any(TestCaseRequestDto.class))).thenReturn(response);

        mockMvc.perform(post("/test-case/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));
    }

    @Test
    @DisplayName("Update test case success")
    void updateTestCaseSuccess() throws Exception {
        TestCase updatedTestcase = new TestCase();
        Map<String, String> response = Map.of("message", "Test case updated successfully");

        Mockito.when(testCaseService.update(any(TestCase.class))).thenReturn(response);

        mockMvc.perform(post("/test-case/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedTestcase)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));
    }

    @Test
    @DisplayName("Delete test case success")
    void deleteTestCaseSuccess() throws Exception {
        Map<String, Integer> requestBody = Map.of("id", 1);
        Map<String, String> response = Map.of("message", "Test case deleted successfully");

        Mockito.when(testCaseService.deleteTestcase(anyInt())).thenReturn(response);

        mockMvc.perform(post("/test-case/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(response)));
    }

    @Test
    @DisplayName("Search test cases success")
    void searchTestCasesSuccess() throws Exception {
        TestCase testCase = new TestCase();
        Prompt prompt = new Prompt();
        prompt.setLlmModel(new LlmModel());
        testCase.setPrompt(prompt);
        List<TestCase> testCases = Collections.singletonList(testCase);

        Mockito.when(testCaseService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(testCases);

        mockMvc.perform(get("/test-case/search")
                        .param("filter", "sample")
                        .param("offset", "0")
                        .param("size", "10")
                        .param("orderBy", "id")
                        .param("orderType", "asc"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("Count test cases success")
    void countTestCasesSuccess() throws Exception {
        Mockito.when(testCaseService.count(anyString())).thenReturn(10L);

        mockMvc.perform(get("/test-case/count")
                        .param("filter", "sample"))
                .andExpect(status().isOk())
                .andExpect(content().string("10"));
    }
}
