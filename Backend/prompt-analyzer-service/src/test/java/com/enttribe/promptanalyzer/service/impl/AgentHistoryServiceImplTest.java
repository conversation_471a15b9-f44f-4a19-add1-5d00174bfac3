package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.model.AgentHistory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.Optional;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class AgentHistoryServiceImplTest {

    @MockitoBean
    private AgentHistoryDao agentHistoryDao;

    @Autowired
    private AgentHistoryServiceImpl agentHistoryServiceImpl;

    private AgentHistory agentHistory;
    private String testAgentName;

    @BeforeEach
    void setUp() {
        testAgentName = "test-agent";
        
        agentHistory = new AgentHistory();
        agentHistory.setId(1);
        agentHistory.setAgentName(testAgentName);
    }

    @Test
    @DisplayName("Get Agent History - Success Case")
    void getAgentHistorySuccess() {
        when(agentHistoryDao.getAgentHistory(testAgentName))
            .thenReturn(Optional.ofNullable(agentHistory));

        AgentHistory result = agentHistoryServiceImpl.getAgentHistory(testAgentName);
        
        assertNotNull(result);
        assertEquals(testAgentName, result.getAgentName());
        verify(agentHistoryDao, times(1)).getAgentHistory(testAgentName);
    }

    @Test
    @DisplayName("Get Agent History - Failure Case")
    void getAgentHistoryFailure() {
        when(agentHistoryDao.getAgentHistory(testAgentName))
            .thenReturn(Optional.empty());

        AgentHistory result = agentHistoryServiceImpl.getAgentHistory(testAgentName);

        assertNull(result);
        verify(agentHistoryDao, times(1)).getAgentHistory(testAgentName);
    }
}
