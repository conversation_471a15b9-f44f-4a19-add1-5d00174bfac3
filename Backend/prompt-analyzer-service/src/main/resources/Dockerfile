# Use a lightweight JDK image
FROM registry.visionwaves.com/alpine-fixed:3.20.3

# Install required packages in a single step to reduce image layers
RUN apk add --no-cache \
    procps \
    bash \
    curl \
    ttf-dejavu && \
    rm -rf /var/cache/apk/*  # Remove package cache to save space

# Set environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8 \
    SSL_VAULT_PATH=/opt/visionwaves/sql_ssl \
    SERVICE_ARCHIVE=prompt-analyzer \
    SERVICE_PATH=/opt/visionwaves/prompt-analyzer \
    BASE_PATH=/opt/visionwaves/prompt-analyzer

# Create a non-root user and group
RUN addgroup -S visionwaves && adduser -S -G visionwaves visionwaves

# Create necessary directories
RUN mkdir -p "$BASE_PATH" "$SSL_VAULT_PATH" && \
    chown -R visionwaves:visionwaves "$BASE_PATH" "$SSL_VAULT_PATH"

# Copy application archive
COPY ./$SERVICE_ARCHIVE.tar $BASE_PATH/

# Extract the `.tar` file after copying it
RUN tar -xvf "$BASE_PATH/$SERVICE_ARCHIVE.tar" -C "$BASE_PATH" && \
    rm "$BASE_PATH/$SERVICE_ARCHIVE.tar"  # Clean up tar file to reduce image size

# Switch to non-root user
USER visionwaves

# Set working directory
WORKDIR $SERVICE_PATH

# Start the service
CMD ["bash", "run.sh", "start"]
