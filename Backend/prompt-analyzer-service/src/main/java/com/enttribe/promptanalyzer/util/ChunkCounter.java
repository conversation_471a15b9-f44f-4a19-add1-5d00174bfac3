package com.enttribe.promptanalyzer.util;

import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;

import java.util.Optional;

public class ChunkCounter {

    private static final Optional<Encoding> encoding =
            Encodings.newDefaultEncodingRegistry().getEncodingForModel("gpt-4-turbo");
    private static final int APPROX_CHARS_PER_TOKEN = 4;

    private ChunkCounter() {
        // Private constructor to hide implicit public one
    }

    public static int countChunks(String text) {
        return encoding.map(e -> e.encode(text).size()).orElse(text.length() / APPROX_CHARS_PER_TOKEN); // Fallback approximation
    }
}
