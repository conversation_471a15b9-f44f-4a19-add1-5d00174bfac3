package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.VectorStoreConfig;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.HintService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class HintServiceImpl implements HintService {

    public static final String HINT_FILTER = "employee";
    private static final String HINT_VALUE = "hintValue";
    private static final String FILTER_EXPRESSION_FORMAT = "'filter' == '%s'";
    private static final String METADATA_KEY = "metadata";

    private static final double DEFAULT_SIMILARITY_THRESHOLD = 0.4;
    private static final double ALT_SIMILARITY_THRESHOLD = 0.6;
    private static final double EXACT_MATCH_SCORE_THRESHOLD = 0.9;
    private static final int DEFAULT_TOP_K = 5;

    private final VectorStoreConfig vectorStoreConfig;

    @Override
    public boolean saveHint(Map<String, Object> requestMap) {
        List<String> hintKeys = (List<String>) requestMap.get("hintKeys");
        String hintValue = (String) requestMap.get(HINT_VALUE);

        try {
            List<Document> hintDocuments = new ArrayList<>();
            for (String hint : hintKeys) {
                log.debug("adding hint: {}", hint);
                Document hintDocument = new Document(hint);
                hintDocument.getMetadata().put("filter", HINT_FILTER);
                hintDocument.getMetadata().put(hintValue, hintValue);
                hintDocuments.add(hintDocument);
            }
            VectorStore vectorStore = vectorStoreConfig.getVectorStore("1");
            vectorStore.accept(hintDocuments);
            log.debug("hints saved successfully");
            return true;
        } catch (Exception e) {
            log.error("error while saving hint: {}", e.getMessage(), e);
            throw new BusinessException("unable to save hint", e);
        }
    }

    @Override
    public String searchPlan(Map<String, String> map) {
        String query = map.get("query");
        log.debug("query for search is {}", query);
        SearchRequest searchRequest = SearchRequest.builder()
                .similarityThreshold(DEFAULT_SIMILARITY_THRESHOLD)
                .filterExpression(String.format(FILTER_EXPRESSION_FORMAT, HINT_FILTER))
                .topK(DEFAULT_TOP_K)
                .query(query)
                .build();

        VectorStore vectorStore = vectorStoreConfig.getVectorStore("1");
        List<Document> documents = vectorStore.similaritySearch(searchRequest);
        if (documents.isEmpty()) return "No hints found";

        return (String) documents.getFirst().getMetadata().get(HINT_VALUE);
    }

    @Override
    public List<Map<String, Object>> searchPlanBatch(List<String> queries) {
        log.debug("Processing batch search with {} queries", queries.size());
        List<Map<String, Object>> results = new ArrayList<>();

        for (String queryText : queries) {
            log.debug("Processing query: {}", queryText);

            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(DEFAULT_SIMILARITY_THRESHOLD)
                    .filterExpression(String.format(FILTER_EXPRESSION_FORMAT, HINT_FILTER))
                    .topK(DEFAULT_TOP_K)
                    .query(queryText)
                    .build();

            VectorStore vectorStore = vectorStoreConfig.getVectorStore("1");
            List<Document> documents = vectorStore.similaritySearch(searchRequest);

            Map<String, Object> resultItem = new java.util.HashMap<>();
            resultItem.put("name", queryText);

            if (!documents.isEmpty()) {
                List<Map<String, Object>> metadata = new ArrayList<>();
                for (Document doc : documents) {
                    metadata.add(doc.getMetadata());
                }
                resultItem.put(METADATA_KEY, metadata);
            } else {
                resultItem.put(METADATA_KEY, List.of());
            }

            results.add(resultItem);
        }
        return results;
    }

    @Override
    public List<Map<String, Object>> searchPlanBatchV1(List<String> queries, String type) {
        log.debug("Processing batch search with {} queries and type {}", queries.size(), type);
        List<Map<String, Object>> results = new ArrayList<>();

        String hintFilter = type.equals("who") ? HINT_FILTER : "What";
        for (String queryText : queries) {
            log.debug("Processing query: {}", queryText);

            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(ALT_SIMILARITY_THRESHOLD)
                    .filterExpression(String.format(FILTER_EXPRESSION_FORMAT, hintFilter))
                    .topK(DEFAULT_TOP_K)
                    .query(queryText)
                    .build();

            VectorStore vectorStore = vectorStoreConfig.getVectorStore("1");
            List<Document> documents = vectorStore.similaritySearch(searchRequest);

            if (!documents.isEmpty() && documents.getFirst().getScore() > EXACT_MATCH_SCORE_THRESHOLD) {
                log.info("Exact match found for the given name.");
                documents =  List.of(documents.getFirst());
            }

            Map<String, Object> resultItem = new java.util.HashMap<>();
            resultItem.put("name", queryText);

            if (!documents.isEmpty()) {
                List<Map<String, Object>> metadata = new ArrayList<>();
                for (Document doc : documents) {
                    metadata.add(doc.getMetadata());
                }
                resultItem.put(METADATA_KEY, metadata);
            } else {
                resultItem.put(METADATA_KEY, List.of());
            }

            results.add(resultItem);
        }
        return results;
    }

}
