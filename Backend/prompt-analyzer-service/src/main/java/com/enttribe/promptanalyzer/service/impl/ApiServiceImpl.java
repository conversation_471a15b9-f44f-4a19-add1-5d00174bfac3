package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.RestTemplateSingleton;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.ApiService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;


@Service
public class ApiServiceImpl implements ApiService {

    private static final Logger log = LoggerFactory.getLogger(ApiServiceImpl.class);

    @Value("${crawl.url}")
    private String crawlUrl;

    @Value("${crawl.token}")
    private String crawlToken;

    @Value("${execution.api.url}")
    private String executionApiUrl;

    @Value("${processorList.api.url}")
    private String processorsListApiUrl;

    private static final RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
    private static final int DEFAULT_PRIORITY = 10;

    @Override
    public String triggerCrawl(String websiteUrl) {
        log.debug("inside @method triggerCrawl. @param : {}", websiteUrl);
        try {
            String url = String.format("%s/crawl", crawlUrl);
            log.debug("Constructed URL for crawl: {}", url);

            // Create headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", String.format("Bearer %s", crawlToken));
            headers.setContentType(MediaType.APPLICATION_JSON);
            log.debug("Headers set for request: {}", headers);

            // Create request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("urls", websiteUrl);
            requestBody.put("priority", DEFAULT_PRIORITY);
            log.debug("Request body created: {}", requestBody);

            // Create HTTP entity with headers and body
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            // Make the request
            ResponseEntity<CrawlResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    CrawlResponse.class
            );
            log.debug("Received response of Crawl: {}", response);

            // Return response body
            var body = response.getBody();
            return body != null ? body.getTaskId() : null;
        } catch (Exception e) {
            throw new BusinessException("error while triggering crawl : {}" + e.getMessage());
        }
    }

    @Override
    public CrawlResponse getTaskStatus(String taskId) {
        log.debug("inside @method getTaskStatus. @param : taskId -> {}", taskId);
        try {
            // Construct the request URL
            String url = String.format("%s/task/%s", crawlUrl, taskId);
            log.debug("Constructed URL for task status: {}", url);

            // Create headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", String.format("Bearer %s", crawlToken));
            headers.setContentType(MediaType.APPLICATION_JSON);
            log.debug("Headers set for request: {}", headers);

            // Create HTTP entity with headers
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            // Make the GET request
            ResponseEntity<CrawlResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    CrawlResponse.class
            );
            log.debug("Received response of task status: {}", response);

            // Return response body
            return response.getBody();
        } catch (Exception e) {
            log.error("error in getting task status : {}", e.getMessage(), e);
            return new CrawlResponse(e.getMessage());
        }
    }

    @Override
    public String getTableSchema(String executionName, String tableName) {
        log.debug("inside @method getTableSchema. @param : executionName -> {}, tableName -> {}", executionName, tableName);
        try {
            String requestBody = String.format(
                    "{\"name\":\"%s\", \"operationSpecification\": \"{\\\"query\\\":\\\"SHOW CREATE TABLE %s\\\"}\"}",
                    executionName, tableName
            );
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
            Map<String, Object> responseMap = restTemplate.postForObject(executionApiUrl, request, Map.class);
            log.debug("Received response of table schema: {}", responseMap);
            if (responseMap != null && "SUCCESS".equals(responseMap.get("response"))) {
                return responseMap.get("sqlResponse").toString();
            } else {
                return null;
            }

        } catch (Exception e) {
            log.error("error while getting table name : {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<String> getTablesName(String name) {
        log.debug("Inside @method getTablesName. @param : name");
        List<String> tableNames = new ArrayList<>();
        try {
            log.debug("RestTemplate initialized successfully");
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            log.debug("HTTP headers set successfully");
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", name);
            requestBody.put("operationSpecification", "{\"query\":\"Show tables;\"}");
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(requestBody);
            log.debug("Request body sqlConnector: {}", name);
            HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
            log.info("Sending API request to: {}", executionApiUrl);
            ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(
                    executionApiUrl,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );
            Map<String, Object> response = responseEntity.getBody();
            log.info("Received API response: {}", response);
            if (response != null && "SUCCESS".equals(response.get("response"))) {
                Object sqlResponseObj = response.get("sqlResponse");

                if (sqlResponseObj instanceof List<?> sqlResponse) {
                    tableNames = sqlResponse.stream()
                            .filter(Map.class::isInstance)
                            .map(map -> (Map<?, ?>) map)
                            .map(entry -> entry.values().iterator().next().toString()) // Extract table names
                            .toList();
                    log.debug("Extracted table names: {}", tableNames);
                } else {
                    log.warn("sqlResponse is null or in an unexpected format");
                }
            } else {
                log.warn("API response status is not SUCCESS or response is : {}", response);
            }
        } catch (Exception e) {
            throw new BusinessException("Failed to retrieve table names", e);
        }
        return tableNames;
    }


    @Override
    public List<Map<String, Object>> getProcessorsListFromApi() {
        log.debug("Inside @method getProcessorsListFromApi");

        try {
            ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                    processorsListApiUrl,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<List<Map<String, Object>>>() {}
            );

            List<Map<String, Object>> resultMap = response.getBody();
            log.debug("Received response of processors list: {}", resultMap);

            return resultMap != null ? resultMap : Collections.emptyList();
        } catch (Exception e) {
            log.error("Error while fetching processors list: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}