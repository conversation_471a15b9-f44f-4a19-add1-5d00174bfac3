package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.rest.TimeParserRest;
import com.enttribe.promptanalyzer.service.TimeParserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * REST controller implementation for time parsing operations.
 * Handles HTTP requests for parsing time-related text from user queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/time-parser")
@RequiredArgsConstructor
public class TimeParserRestImpl implements TimeParserRest {

    private final TimeParserService timeParserService;

    /**
     * Parses a user query for time dimensions.
     *
     * @param request Map containing the user query
     * @return Map containing the parsed time information
     */
    @Override
    public Map<String, String> parseTime(Map<String, String> request) {
        log.info("Received request to parse time from query");
        
        String userQuery = request.get("userQuery");
        if (userQuery == null || userQuery.isEmpty()) {
            log.warn("Empty user query received");
            return Map.of("result", "No query provided");
        }
        
        log.info("Parsing time from query: {}", userQuery);
        String result = timeParserService.parseTimeFromQuery(request);
        
        return Map.of("result", result);
    }

}
