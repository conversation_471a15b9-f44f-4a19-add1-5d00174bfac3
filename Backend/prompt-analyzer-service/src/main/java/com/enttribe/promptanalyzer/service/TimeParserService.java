package com.enttribe.promptanalyzer.service;

import java.util.Map;

/**
 * Service for parsing time-related text from user queries.
 * This service handles the extraction and formatting of time information
 * from natural language queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TimeParserService {

    /**
     * Parses a user query for time dimensions.
     *
     * @param request The user query to parse
     * @return The parsed time information as a string
     */
    String parseTimeFromQuery(Map<String, String> request);

}
