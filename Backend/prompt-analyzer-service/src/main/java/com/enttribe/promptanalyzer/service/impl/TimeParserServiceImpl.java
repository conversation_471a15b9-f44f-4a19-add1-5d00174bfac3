package com.enttribe.promptanalyzer.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.enttribe.promptanalyzer.service.TimeParserService;
import edu.stanford.nlp.ling.CoreAnnotations;
import edu.stanford.nlp.ling.CoreLabel;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TimeParserServiceImpl implements TimeParserService {

    @Value("${time.parser.url}")
    private String timeParserUrl;

    @Value("${time.parser.timezone:UTC}")
    private String timeParserTimeZone;

    private static final String VALUE_KEY = "value";
    private static final String TIME_DIMENSION = "time";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int WEEK_DAYS = 7;

    // Working week configuration
    private static final int WEEKEND_START_DAY = 5;   // Friday (DayOfWeek.FRIDAY.getValue())

    private final RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

    private static final StanfordCoreNLP PIPELINE;
    private static final Pattern MONTH_REGEX =
            Pattern.compile("\\b(january|february|march|april|may|june|july|august|" +
                    "september|october|november|december)\\b", Pattern.CASE_INSENSITIVE);

    private static final String MONTHS =
            "(january|february|march|april|may|june|july|august|" +
                    "september|october|november|december)";

    private static final String QWORDS =
            "(first|1st|q1|second|2nd|q2|third|3rd|q3|fourth|4th|q4)\\s*(?:quarter|qtr)?";

    private static final LevenshteinDistance LV = LevenshteinDistance.getDefaultInstance();

    static {
        Properties p = new Properties();
        p.setProperty("annotators", "tokenize,ssplit,pos,lemma");
        PIPELINE = new  StanfordCoreNLP(p);
    }

    private static final Map<String,String> TABLE = Map.ofEntries(
            Map.entry("jan",  "january"),  Map.entry("january","january"),
            Map.entry("feb",  "february"), Map.entry("february","february"),
            Map.entry("mar",  "march"),    Map.entry("march","march"),
            Map.entry("apr",  "april"),    Map.entry("april","april"),
            Map.entry("may",  "may"),
            Map.entry("jun",  "june"),     Map.entry("june","june"),
            Map.entry("jul",  "july"),     Map.entry("july","july"),
            Map.entry("aug",  "august"),   Map.entry("august","august"),
            Map.entry("sep",  "september"),Map.entry("sept","september"),
            Map.entry("september","september"),
            Map.entry("oct",  "october"),  Map.entry("october","october"),
            Map.entry("nov",  "november"), Map.entry("november","november"),
            Map.entry("dec",  "december"), Map.entry("december","december")
    );
    /* month or quarter followed by optional 4-digit year */
    private static final Pattern P = Pattern.compile(
            "\\b(" + QWORDS + "|" + MONTHS + ")(?:\\s+(\\d{4}))?\\b",
            Pattern.CASE_INSENSITIVE);

    public static String augment(String query, ZoneId zone, Tense tense) {

        Matcher m = P.matcher(query);
        StringBuffer sb = new StringBuffer();
        LocalDate today = LocalDate.now(zone);
        int thisYear    = today.getYear();

        while (m.find()) {
            String token   = m.group(1);
            String yearStr = m.group(2);

            // if user already typed a year → leave token unchanged
            if (yearStr != null) {
                m.appendReplacement(sb, m.group());
                continue;
            }

            /* -------- Quarter handling -------- */
            int quarter = quarterNumber(token);
            if (quarter != 0) {
                LocalDate startQ = LocalDate.of(thisYear, 1 + (quarter - 1) * 3, 1);
                LocalDate endQ   = startQ.plusMonths(3);      // exclusive

                switch (tense) {
                    case FUTURE, PRESENT ->
                            startQ = endQ.isBefore(today) ? startQ.plusYears(1) : startQ;
                    case PAST ->
                            startQ = startQ.isAfter(today) ? startQ.minusYears(1) : startQ;
                }
                m.appendReplacement(sb, token + " " + startQ.getYear());
                continue;
            }

            /* -------- Month handling (typo-tolerant) -------- */
            String canon = canonicalMonth(token);
            if (canon != null) {
                int monthNum = Month.valueOf(canon.toUpperCase(Locale.ROOT)).getValue();
                int year = switch (tense) {
                    case FUTURE, PRESENT ->
                            (monthNum < today.getMonthValue()) ? thisYear + 1 : thisYear;
                    case PAST ->
                            (monthNum > today.getMonthValue()) ? thisYear - 1 : thisYear;
                };
                m.appendReplacement(sb, canon + " " + year);
                continue;
            }

            // fallback – not month/quarter
            m.appendReplacement(sb, m.group());
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /** 1-4, or 0 if token is not a quarter word. */
    private static int quarterNumber(String token) {
        token = token.toLowerCase(Locale.ROOT).replaceAll("\\s+", "");
        return switch (token) {
            case "first","1st","q1"   -> 1;
            case "second","2nd","q2"  -> 2;
            case "third","3rd","q3"   -> 3;
            case "fourth","4th","q4"  -> 4;
            default -> 0;
        };
    }

    /** returns canonical month or null */
    private static String canonicalMonth(String token) {
        String[][] map = {
                {"jan","january"},{"feb","february"},{"mar","march"},
                {"apr","april"},{"may","may"},{"jun","june"},{"jul","july"},
                {"aug","august"},{"sep","september"},{"sept","september"},
                {"oct","october"},{"nov","november"},{"dec","december"}
        };
        String low = token.toLowerCase(Locale.ROOT);
        for (String[] row : map) {
            String abbr = row[0], full = row[1];
            int dist = LV.apply(low, full);
            if (dist == 0 || (full.length() >= 5 && dist <= 2) || (full.startsWith(low) && low.length() >= 3))
                return full;
            if (low.equals(abbr)) return full;
        }
        return null;
    }
    /** Null if token is not (even fuzzy) a month. */
    public static String canonical(String token) {
        return TABLE.get(token.toLowerCase());
    }

    /** int 1-12 or -1 if not a month. */
    public static int monthNumber(String token) {
        String c = canonical(token);
        return c == null ? -1 : Month.valueOf(c.toUpperCase()).getValue();
    }

    public enum Tense { PAST, PRESENT, FUTURE }



    /** Main entry: pass the raw user query, get a query with years filled in. */
    public static String addYearIfMissing(String query, ZoneId zone) {
        String[] parts = query.split("\\b");
        Year now = Year.now(zone);
        Tense tense = detectTense(query);
        log.debug("Tense detected is{} ",tense.toString());

        String preprocessed =  augment(query, zone, tense);
        log.debug("Preprocessed String {} ",preprocessed);
        return  preprocessed;
    }

    /* ----------- tense detector (very simple heuristic) ------------- */
    private static Tense detectTense(String text) {
        Annotation doc = new Annotation(text);
        PIPELINE.annotate(doc);

        boolean hasPast = false, hasFuture = false;
        List<CoreLabel> toks = doc.get(CoreAnnotations.TokensAnnotation.class);

        for (int i = 0; i < toks.size(); i++) {
            CoreLabel tok   = toks.get(i);
            String pos      = tok.get(CoreAnnotations.PartOfSpeechAnnotation.class); // VBD, MD, ...
            String lemma    = tok.get(CoreAnnotations.LemmaAnnotation.class).toLowerCase();

            /* 1 ─ Past tense or past perfect */
            if (pos.equals("VBD") || pos.equals("VBN")) {
                hasPast = true;
            }

            /* 2 ─ Canonical future modal  ("will", "shall") */
            if (pos.equals("MD") && ("will".equals(lemma) || "shall".equals(lemma))) {
                hasFuture = true;
                continue;
            }

            /* 3 ─ “be going to <VB>” future */
            if ("going".equals(lemma) && i >= 1 && i + 1 < toks.size()) {
                String prevLemma = toks.get(i - 1).lemma().toLowerCase();
                String nextPos   = toks.get(i + 1).tag();                 // should be "TO"
                if (("be".equals(prevLemma) || "am".equals(prevLemma) ||
                        "is".equals(prevLemma) || "are".equals(prevLemma)) &&
                        "TO".equals(nextPos)) {
                    hasFuture = true;
                    continue;
                }
            }

            /* 4 ─ Intent verbs  (“plan / planning / intend”) +  "to <VB>" */
            if ((lemma.equals("plan") || lemma.equals("intend")) &&
                    i + 1 < toks.size() &&
                    "TO".equals(toks.get(i + 1).tag())) {
                hasFuture = true;
            }
        }

        /* Decide tense */
        Tense tense;
        if (hasFuture && !hasPast)      tense = Tense.FUTURE;
        else if (hasPast && !hasFuture) tense = Tense.PAST;
        else                            tense = Tense.PRESENT;
        return tense;
    }

    @Override
    public String parseTimeFromQuery(Map<String, String> request) {
        String userQuery = request.get("userQuery");
        String timeZone = request.get("timeZone");
        String locale = request.get("locale");
        locale = locale != null ? locale : "en_US";
        timeZone = timeZone != null ? timeZone : timeParserTimeZone;
        ZoneId userZone  = ZoneId.of(timeZone);
        log.info("Parsing time from query: {} timeZone: {} locale: {}", userQuery, timeZone, locale);
        try {
            // ✦ NEW: augment month without year ✦
            String preprocessed = addYearIfMissing(userQuery, userZone);
            log.debug("Query after month-year augmentation: {}", preprocessed);
            // Make the initial API call
            String responseBody = callTimeParserApi(preprocessed, timeZone, locale);
            log.debug("Received response: {}", responseBody);
            System.out.println(responseBody);
            // Process the response
            return processTimeResponse(responseBody, userQuery.toLowerCase());
        } catch (Exception e) {
            log.error("Error parsing time from query: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to parse time from query");
        }
    }

    /**
     * Calls the time parser API with the given query.
     *
     * @param query    The query to parse for time information
     * @param timeZone The time zone to use for parsing
     * @param locale   The locale to use for parsing
     * @return The response from the API
     */
    private String callTimeParserApi(String query, String timeZone, String locale) {
        // Prepare request headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // Prepare request body
        String requestBody = String.format("locale=%s&tz=%s&text=%s&dims=[\"%s\"]", locale, timeZone, query, TIME_DIMENSION);

        // Create the request entity
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        // Make the API call
        return restTemplate.postForObject(timeParserUrl, requestEntity, String.class);
    }

    /**
     * Processes the response from the time parser API.
     *
     * @param responseBody The JSON response from the API
     * @return The formatted time information
     * @throws JsonProcessingException If there's an error processing the JSON
     */
    /**
     * Processes the response from Duckling and chooses the
     * “strongest” time entity instead of always using index 0.
     */
    private String processTimeResponse(String responseBody,
                                       String originalQuery) throws JsonProcessingException {

        log.debug("Duckling response, {}",responseBody);
        JsonNode rootNode = objectMapper.readTree(responseBody);

        if (!rootNode.isArray() || rootNode.isEmpty()) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        /* ---------- pick best candidate ---------- */
        JsonNode bestNode = null;
        int      bestRank = Integer.MAX_VALUE;  // lower = better
        int      bestSpan = -1;                 // char length of body

        for (JsonNode node : rootNode) {
            String grain = node.path("value").path("grain").asText("");
            int rank = switch (grain) {
                case "quarter" -> 1;
                case "month"   -> 2;
                case "week"    -> 3;
                case "day"     -> 4;
                case "hour"    -> 5;
                case "minute"  -> 6;
                case "second"  -> 7;
                default        -> 8;
            };
            int span = node.path("end").asInt() - node.path("start").asInt();

            if (rank < bestRank || (rank == bestRank && span > bestSpan)) {
                bestRank = rank;
                bestSpan = span;
                bestNode = node;
            }
        }

        if (bestNode == null) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        /* ---------- process the chosen node ---------- */
        String body      = bestNode.path("body").asText("").toLowerCase();
        JsonNode value   = bestNode.path(VALUE_KEY);

        return processExtractedTimeValue(value, body, originalQuery);
    }


    private String processExtractedTimeValue(JsonNode valueNode,
                                             String body,
                                             String originalQuery) {
        log.debug("Processing extracted time value: {} with body: {}", valueNode, body);
        if (valueNode == null) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        String type = valueNode.get("type").asText();
        boolean isWeekendQuery = body.contains("weekend") || originalQuery.contains("weekend");

        return switch (type) {
            case VALUE_KEY   -> processValueType(valueNode, isWeekendQuery, originalQuery);
            case "interval"  -> processIntervalType(valueNode, isWeekendQuery, originalQuery); // ★ changed
            case "array"     -> processArrayType(valueNode, isWeekendQuery);
            default          -> {
                log.info("Unsupported time type: {}", type);
                yield "Unsupported time type: " + type;
            }
        };
    }

    /**
     * Handles Duckling objects whose "type" == "value".
     *
     * Added rule for MONTH grain
     * --------------------------
     * • When the user did not supply a year and Duckling returns several
     *   future years (january → 2026/2027/2028), we substitute the first
     *   value with <CURRENT-YEAR>-MM-01T00:00… so the month is anchored in
     *   the present year, never in the future.
     */
    /**
     * Handles Duckling objects whose "type" == "value".
     *
     * MONTH grain logic
     * ─────────────────
     * • values.size() > 1  → anchor to CURRENT year (same month/day, 00:00)
     * • values.size() == 1 → keep Duckling’s ISO as-is
     *
     * All other grains retain previous behaviour.
     */
    private String processValueType(JsonNode valueNode,
                                    boolean isWeekendQuery,
                                    String originalQuery) {

        String grain = valueNode.has("grain")
                ? valueNode.get("grain").asText()
                : "day";

        JsonNode valuesNode = valueNode.get("values");

        /* ── MONTH grain special-case ─────────────────────────────────── */
        if ("month".equalsIgnoreCase(grain)) {

            // When Duckling offers multiple future years for an unspecified month
            if (valuesNode != null && valuesNode.size() > 1) {

                String firstIso = valuesNode.get(0).get(VALUE_KEY).asText();
                ZonedDateTime firstDate = parseDateTime(firstIso);

                ZonedDateTime adjusted = firstDate
                        .withYear(ZonedDateTime.now(firstDate.getZone()).getYear())
                        .withHour(0).withMinute(0).withSecond(0).withNano(0);

                return adjusted.toString();          // e.g. 2025-01-01T00:00+03:00
            }

            // Single candidate – keep exactly what Duckling returned
            String iso = (valuesNode != null && valuesNode.size() == 1)
                    ? valuesNode.get(0).get(VALUE_KEY).asText()
                    : valueNode.get(VALUE_KEY).asText();

            return iso;
        }

        /* ── Weekend “hour” override (unchanged) ─────────────────────── */
        if (isWeekendQuery && "hour".equalsIgnoreCase(grain) &&
                valuesNode != null && valuesNode.size() > 0) {

            String dateStr = valuesNode.get(0).get(VALUE_KEY).asText();
            return processDateByGrain(dateStr, grain, true, originalQuery);
        }

        /* ── All other grains keep existing behaviour ────────────────── */
        if (valuesNode != null && valuesNode.size() > 0) {
            StringBuilder sb = new StringBuilder();
            for (JsonNode v : valuesNode) {
                if (sb.length() > 0) sb.append(" OR ");
                sb.append(processDateByGrain(
                        v.get(VALUE_KEY).asText(), grain, isWeekendQuery, originalQuery));
            }
            return sb.toString();
        }

        if (valueNode.has(VALUE_KEY)) {
            return processDateByGrain(
                    valueNode.get(VALUE_KEY).asText(), grain, isWeekendQuery, originalQuery);
        }

        return "PromptConstants.NO_TIME_INFORMATION_FOUND";
    }


    /**
     * Handles Duckling intervals, including cases where Duckling supplies
     * only one bound (e.g. “from last week”, “until next month”).
     *
     * Rules for fabricating the missing bound
     * ---------------------------------------
     *  grain == day    → ±1 day
     *  grain == week   → Saudi business week based on the supplied date
     *  grain == month  → ±1 calendar month (JDK handles 28/29/30/31)
     *  grain == year   → ±1 calendar year (365 / 366 handled by java.time)
     *
     * Weekend queries (“this weekend”) are still mapped to the Fri–Sat range.
     */
    private String processIntervalType(JsonNode valueNode,
                                       boolean isWeekendQuery,
                                       String originalQuery) {

        JsonNode fromNode = valueNode.get("from");
        JsonNode toNode   = valueNode.get("to");

        /* 0 ─ Weekend shortcut ─────────────────────────────────────────── */
        if (isWeekendQuery && fromNode != null) {
            ZonedDateTime fromDt = parseDateTime(fromNode.get(VALUE_KEY).asText());
            return processWeekend(fromDt);                     // Fri–Sat
        }

        /* 1 ─ Both bounds present → simple clean range ─────────────────── */
        if (fromNode != null && toNode != null) {
            return String.format("from: %s to: %s",
                    fromNode.get(VALUE_KEY).asText(),
                    toNode  .get(VALUE_KEY).asText());
        }

        /* helper: parse ISO to ZonedDateTime */
        java.util.function.Function<JsonNode, ZonedDateTime> toDate =
                n -> parseDateTime(n.get(VALUE_KEY).asText());

        /* 2 ─ Only FROM present → fabricate TO ─────────────────────────── */
        if (fromNode != null) {
            String grain = fromNode.has("grain") ? fromNode.get("grain").asText() : "";
            ZonedDateTime start = toDate.apply(fromNode);

            return switch (grain.toLowerCase()) {
                case "day"   -> formatDateRange(start, start.plusDays(1));
                case "week"  -> processWorkWeek(ZonedDateTime.now(start.getZone()), "last week");
                case "month" -> formatDateRange(start, start.plusMonths(1));
                case "year"  -> formatDateRange(start, start.plusYears(1));
                default      -> String.format("from: %s", start.toString());
            };
        }

        /* 3 ─ Only TO present → fabricate FROM (rare) ─────────────────── */
        if (toNode != null) {
            String grain = toNode.has("grain") ? toNode.get("grain").asText() : "";
            ZonedDateTime end = toDate.apply(toNode);

            return switch (grain.toLowerCase()) {
                case "day"   -> formatDateRange(end.minusDays(1), end);
                case "week"  -> processWorkWeek(ZonedDateTime.now(end.getZone()), "last week");
                case "month" -> formatDateRange(end.minusMonths(1), end);
                case "year"  -> formatDateRange(end.minusYears(1), end);
                default      -> String.format("to: %s", end.toString());
            };
        }

        return "PromptConstants.NO_TIME_INFORMATION_FOUND";
    }

    private String processArrayType(JsonNode arrayNode, boolean isWeekendQuery) {
        if (!arrayNode.isArray()) {
            return "PromptConstants.NO_TIME_INFORMATION_FOUND";
        }

        StringBuilder result = new StringBuilder();
        for (JsonNode item : arrayNode) {
            if (result.length() > 0) {
                result.append(" AND ");
            }
            result.append(processExtractedTimeValue(item, "", ""));
        }
        return result.toString();
    }


    /**
     * Converts one ISO timestamp from Duckling into the desired output.
     *
     * • WEEK   → Saudi business-week (Sun-Thu) range
     * • MONTH  → single ISO date (YYYY-MM-01T00:00…) – no range, no year shifting
     * • Other grains keep their existing range behaviour.
     */
    private String processDateByGrain(String dateValue,
                                      String grain,
                                      boolean isWeekendQuery,
                                      String originalQuery) {
        log.debug("Processing date by grain: {}  grain={} weekend={}",
                dateValue, grain, isWeekendQuery);

        try {
            ZonedDateTime dt = parseDateTime(dateValue);

            /* Weekend queries override everything */
            if (isWeekendQuery) {
                return processWeekend(dt);
            }

            switch (grain.toLowerCase()) {

                /* ── SECOND / MINUTE / HOUR ─────────────────────────────── */
                case  "minute", "hour" -> {
                    ZonedDateTime end = dt.plusHours(1);
                    return formatDateRange(dt, end);
                }

                /* ── DAY ───────────────────────────────────────────────── */
                case "day" -> {
                    boolean wholeDay = dt.getHour() == 0 && dt.getMinute() == 0;
                    ZonedDateTime end = wholeDay ? dt.plusDays(1)
                            : dt.plusHours(1);
                    return formatDateRange(dt, end);
                }

                /* ── WEEK  (Saudi business week) ─────────────────────── */
                case "week" -> {
                    return processWorkWeek(dt, originalQuery);
                }

                /* ── MONTH  (single ISO, no year correction) ─────────── */
                case "month" -> {
                    ZonedDateTime normalized = dt.withHour(0).withMinute(0)
                            .withSecond(0).withNano(0);
                    return normalized.toString();
                }

                /* ── QUARTER ─────────────────────────────────────────── */
                case "quarter" -> {
                    // normalise the start to the first day 00:00
                    ZonedDateTime start = dt.withDayOfMonth(1)
                            .withHour(0).withMinute(0)
                            .withSecond(0).withNano(0);

                    // exclusive upper bound: start + 3 months
                    ZonedDateTime end = start.plusMonths(3);

                    return formatDateRange(start, end);
                }


                /* ── YEAR ────────────────────────────────────────────── */
                case "year" -> {
                    ZonedDateTime start = dt.with(TemporalAdjusters.firstDayOfYear());
                    ZonedDateTime end   = dt.with(TemporalAdjusters.lastDayOfYear())
                            .plusDays(1);
                    return formatDateRange(start, end);
                }

                case "decade" -> {
                    ZonedDateTime end = dt.plusYears(10);
                    return formatDateRange(dt, end);
                }
                case "century" -> {
                    ZonedDateTime end = dt.plusYears(100);
                    return formatDateRange(dt, end);
                }
                case "millennium" -> {
                    ZonedDateTime end = dt.plusYears(1000);
                    return formatDateRange(dt, end);
                }
                /* ── fallback: leave untouched ───────────────────────── */
                //default -> dateValue;
            }

        } catch (Exception e) {
            log.warn("Error processing date by grain: {}", e.getMessage());
            return dateValue;
        }
        return dateValue;
    }

    /**
     * Builds a range for a Saudi working week (Sun 00:00 → Fri 00:00).
     *
     * <p>Duckling always gives you the ISO-8601 Monday of the week that the
     * expression refers to.  That works fine for “next week” and “last week”,
     * but when <b>today is Sunday</b> (Saudi start-of-week) Duckling’s Monday is
     * <i>six days ago</i>.  To get the right Sunday we:</p>
     *
     * <ol>
     *   <li>Detect whether the query is “this”, “next”, or “last” week by looking
     *       at <code>originalQuery</code>.</li>
     *   <li>Choose a <i>reference date</i> based on that:
     *       <ul>
     *         <li>this/current week → “now”</li>
     *         <li>next week         → now&nbsp;+ 7 days</li>
     *         <li>last/previous     → now&nbsp;− 7 days</li>
     *       </ul></li>
     *   <li>Snap that reference date backwards (or keep it) to the
     *       <b>nearest Sunday 00:00</b>.</li>
     * </ol>
     *
     * @param mondayStart   The ISO Monday provided by Duckling
     * @param originalQuery The user’s raw query (lower/upper case doesn’t matter)
     * @return "from: <Sun 00:00> to: <Fri 00:00>"
     */

    private String processWorkWeek(ZonedDateTime mondayStart,
                                   String originalQuery) {

        ZoneId zone = mondayStart.getZone();
        ZonedDateTime today = ZonedDateTime.now(zone);

        String q = originalQuery == null ? "" : originalQuery.toLowerCase();

        /* ---------- choose reference date --------------------------- */
        ZonedDateTime ref;
        if (q.contains("next") && !q.contains("last")) {
            ref = today.plusWeeks(1);
        } else if (q.contains("last") || q.contains("previous")) {
            ref = today.minusWeeks(1);
        } else {
            // no relative keyword → use mondayStart *unless* it is the same ISO week as today
            int thisIsoWeek  = today.get(java.time.temporal.IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int startIsoWeek = mondayStart.get(java.time.temporal.IsoFields.WEEK_OF_WEEK_BASED_YEAR);

            ref = (thisIsoWeek == startIsoWeek && today.getYear() == mondayStart.getYear())
                    ? today              // "this week"
                    : mondayStart;       // absolute week supplied by Duckling
        }

        /* ---------- snap to Sunday 00:00 and build Saudi range ------ */
        ZonedDateTime weekStart = ref
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)) // ← FIX
                .withHour(0).withMinute(0)
                .withSecond(0).withNano(0);

        // exclusive upper bound: Friday 00:00 (Sun + 5 days)
        ZonedDateTime weekEnd = weekStart.plusDays(5);

        return formatDateRange(weekStart, weekEnd);
    }



    /**
     * Process weekend (Friday and Saturday)
     */
    /**
     * Builds a Saudi weekend range:
     *          Friday 00:00 (inclusive)
     *    →     Sunday 00:00 (exclusive)
     * so the range covers **all of Friday and Saturday only**.
     *
     * @param dateTime  any timestamp that Duckling gave inside the target
     *                  weekend (could be Fri, Sat, or an hour on either day)
     * @return  "from: <ISO> to: <ISO>" string
     */
    private String processWeekend(ZonedDateTime dateTime) {

        /* 1 ─ Walk back (if needed) until we land on Friday */
        ZonedDateTime weekendStart = dateTime;
        while (weekendStart.getDayOfWeek().getValue() != WEEKEND_START_DAY) { // 5 == Friday
            weekendStart = weekendStart.minusDays(1);
        }

        /* 2 ─ Normalise to Friday 00:00 */
        weekendStart = weekendStart.withHour(0).withMinute(0)
                .withSecond(0).withNano(0);

        /* 3 ─ Exclusive upper bound: Sunday 00:00 (Friday + 2 days) */
        ZonedDateTime weekendEnd = weekendStart.plusDays(2);

        /* 4 ─ Return formatted range */
        return formatDateRange(weekendStart, weekendEnd);
    }

    /**
     * Parses a date-time string from the API into a ZonedDateTime object.
     *
     * @param dateTimeStr The date-time string to parse
     * @return Parsed ZonedDateTime object
     */
    private ZonedDateTime parseDateTime(String dateTimeStr) {
        // The API returns dates in ISO-8601 format
        return ZonedDateTime.parse(dateTimeStr);
    }

    /**
     * Formats a date range as a string.
     *
     * @param start The start date-time
     * @param end The end date-time
     * @return Formatted date range string
     */
    private String formatDateRange(ZonedDateTime start, ZonedDateTime end) {
        return String.format("from: %s to: %s", start.toString(), end.toString());
    }

    public static void main(String[] args) {
        TimeParserServiceImpl timeParserService = new TimeParserServiceImpl();

        // Test cases
        java.util.Map<String, String> request = new java.util.HashMap<>();
        request.put("timeZone", "Asia/Riyadh"); // Set Saudi timezone
        request.put("locale", "en_US");

        // Test Case 1: When today is Sunday
        request.put("userQuery", "Joining in first week of June");
        runTest(timeParserService, request);


    }

    private static void runTest(TimeParserServiceImpl service, java.util.Map<String, String> request) {
        try {
            System.out.println("Input Query: " + request.get("userQuery"));
            System.out.println("TimeZone: " + request.get("timeZone"));
            System.out.println("Locale: " + request.get("locale"));

            String result = service.parseTimeFromQuery(request);

            System.out.println("\nFinal Output:");
            System.out.println(result);
            System.out.println("=====================================");
        } catch (Exception e) {
            System.err.println("Error parsing time: " + e.getMessage());
            e.printStackTrace();
        }
    }

    class RestTemplateSingleton {

        /**
         * The singleton instance of {@link RestTemplate}.
         */
        private static RestTemplate restTemplate;

        /**
         * Private constructor to prevent instantiation of this utility class.
         * This enforces the singleton pattern by making the class non-instantiable.
         */
        private RestTemplateSingleton() {
            // Private constructor to prevent instantiation
        }

        /**
         * Returns the singleton instance of {@link RestTemplate}.
         * If the instance doesn't exist, it creates a new one.
         *
         * @return the singleton {@link RestTemplate} instance
         */
        public static RestTemplate getRestTemplate() {
            if (restTemplate == null) {
                restTemplate = new RestTemplate();
            }
            return restTemplate;
        }
    }
}