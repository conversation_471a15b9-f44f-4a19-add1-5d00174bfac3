package com.enttribe.promptanalyzer.config;


import com.enttribe.promptanalyzer.ai.dto.VectorMetaData;
import com.enttribe.promptanalyzer.config.milvusconfig.MilvusServiceClientProperties;
import com.enttribe.promptanalyzer.config.milvusconfig.MilvusVectorStoreProperties;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.ModelOptionsUtils;
import com.enttribe.promptanalyzer.util.VectorUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.observation.ObservationRegistry;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
import org.springframework.ai.vectorstore.observation.VectorStoreObservationConvention;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Configuration class responsible for managing and initializing Vector Store instances in the application.
 * This class handles the setup and configuration of vector stores, particularly focusing on Milvus vector
 * store implementation. It provides functionality to create, manage, and retrieve vector store instances
 * based on configuration parameters.
 *
 * <p>The class reads vector store configurations from JSON properties and supports dynamic initialization
 * of multiple vector store instances. It primarily works with Milvus vector stores but is designed to be
 * extensible for other vector store implementations.
 *
 * <p>Key features:
 * <ul>
 *     <li>Dynamic vector store initialization from JSON configuration</li>
 *     <li>Support for Milvus vector store with customizable parameters</li>
 *     <li>Thread-safe vector store instance management</li>
 *     <li>Integration with Spring AI's embedding models and observation registry</li>
 * </ul>
 *
 * <AUTHOR>
 * @see MilvusVectorStore
 * @see VectorStore
 * @see InferenceManager
 */
@Component
@EnableConfigurationProperties({ MilvusServiceClientProperties.class, MilvusVectorStoreProperties.class })
public class VectorStoreConfig {

    /**
     * JSON configuration property key for vector store settings.
     */
    private static String vectorStoreConfigJson = "W10=";//base 64 encoded []

    /**
     * Thread-safe map storing vector store instances with their corresponding keys.
     */
    private Map<String, VectorStore> vectorStoreMap = new HashMap<>();

    private static final Logger log = LoggerFactory.getLogger(VectorStoreConfig.class);

    private final MilvusVectorStoreProperties milvusVectorStoreProperties;
    private final MilvusServiceClientProperties milvusServiceClientProperties;
    private final InferenceManager inferenceManager;
    private final BatchingStrategy batchingStrategy;
    private final ObjectProvider<ObservationRegistry> observationRegistry;
    private final ObjectProvider<VectorStoreObservationConvention> customObservationConvention;
    private Map<String, Map<String, Object>> vectorIdMap = new HashMap<>();

    /**
     * Constructs a new VectorStoreConfig with the specified dependencies.
     *
     * @param milvusVectorStoreProperties   Milvus vector store configuration properties
     * @param milvusServiceClientProperties Milvus service client configuration properties
     * @param inferenceManager              Manager for handling inference models
     * @param batchingStrategy              Strategy for batching operations
     * @param observationRegistry           Registry for observations and metrics
     * @param customObservationConvention   Custom convention for vector store observations
     */
    public VectorStoreConfig(MilvusVectorStoreProperties milvusVectorStoreProperties, MilvusServiceClientProperties milvusServiceClientProperties,
                             InferenceManager inferenceManager, BatchingStrategy batchingStrategy,
                             ObjectProvider<ObservationRegistry> observationRegistry, ObjectProvider<VectorStoreObservationConvention> customObservationConvention,
                             @Value("${commons.ai.sdk.vector_store.config:W10=}") String vectorStoreConfigJson) {
        this.milvusVectorStoreProperties = milvusVectorStoreProperties;
        this.milvusServiceClientProperties = milvusServiceClientProperties;
        this.inferenceManager = inferenceManager;
        this.batchingStrategy = batchingStrategy;
        this.observationRegistry = observationRegistry;
        this.customObservationConvention = customObservationConvention;
        log.info("commons.ai.sdk.vector_store.config: {}", vectorStoreConfigJson);
        setVectorStoreConfigJson(vectorStoreConfigJson); // ✅ Moved static assignment into static method
    }
    private static void setVectorStoreConfigJson(String encodedJson) {
        try {
            vectorStoreConfigJson = new String(Base64.getDecoder().decode(encodedJson));
        } catch (IllegalArgumentException e) {
            log.error("Invalid Base64 for vectorStoreConfigJson: {}, falling back to empty list []", encodedJson, e);
            vectorStoreConfigJson = "[]";
        }
    }

    /**
     * Parses the vector store configuration from JSON.
     *
     * @return List of configuration maps for vector stores
     */
    private List<Map<String, Object>> parseVectorStoreConfigJson() {
        log.info("commons.ai.sdk.vector_store.config : {}", vectorStoreConfigJson);
        try {
            ObjectMapper objectMapper = JsonUtils.getObjectMapper();
            return objectMapper.readValue(VectorStoreConfig.vectorStoreConfigJson, new TypeReference<List<Map<String, Object>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("error while parsing commons.ai.sdk.vector_store.config", e);
            return List.of();
        }
    }

    /**
     * Initializes vector store instances based on the configuration.
     * This method is called automatically after bean construction.
     */
    @PostConstruct
    public void init() {

        log.info("vectorStore instances are (before) : {}", vectorStoreMap.keySet());
        List<Map<String, Object>> vectorStoreConfigList = parseVectorStoreConfigJson();
        log.info("vectorStoreConfigList is : {}", vectorStoreConfigList);

        Map<String, VectorStore> vectorStoreTempMap = new HashMap<>();
        for (Map<String, Object> vectorStoreConfig : vectorStoreConfigList) {
            String vectorDatabase = (String) vectorStoreConfig.get("vectorDatabase");
            VectorUtils.validateVectorDatabaseName(vectorDatabase);
            if ("milvus".equals(vectorDatabase)) {
                VectorStore milvusVectorStore = getMilvusVectorStoreInstance(vectorStoreConfig);
                String vectorStoreKey = VectorUtils.generateMilvusVectorStoreKey(vectorStoreConfig, milvusVectorStoreProperties);
                vectorStoreTempMap.put(vectorStoreKey, milvusVectorStore);
            }
        }

        if (!vectorStoreTempMap.isEmpty()) {
            log.info("updating vector store map");
            updateVectorStoreMap(vectorStoreTempMap);
            log.info("vector store map updated successfully");
        } else {
            log.warn("vector store map is empty");
        }
        log.info("vectorStore instances are (after) : {}", vectorStoreMap.keySet());

        this.vectorIdMap = prepareVectorIdMap(vectorStoreConfigList);
        log.info("vectorIdMap is prepared for keys : {}", vectorIdMap.keySet());
    }

    public void updateVectorStoreMap(Map<String, VectorStore> newMap) {
        if (!newMap.isEmpty()) {
            log.info("Updating vector store map");
            vectorStoreMap = Map.copyOf(newMap);
        }
    }

    private Map<String, Map<String, Object>> prepareVectorIdMap(List<Map<String, Object>> vectorStoreConfigList) {
        Map<String, Map<String, Object>> tempVectorIdMap = new HashMap<>();
        for (Map<String, Object> vectorStoreConfig : vectorStoreConfigList) {
            String vectorStoreId = (String) vectorStoreConfig.get("vector_store_id");
            if (vectorStoreId == null || vectorStoreId.isEmpty()) {
                log.warn("there is no vector_store_id property in object of commons.ai.sdk.vector_store.config. consider adding one");
                continue;
            }
            tempVectorIdMap.put(vectorStoreId, vectorStoreConfig);
        }
        return tempVectorIdMap;
    }

    /**
     * Retrieves a vector store instance based on the provided metadata.
     *
     * @param metaData Metadata containing information to identify the vector store
     * @return The corresponding vector store instance
     * @throws IllegalArgumentException if metaData is null
     */
    public VectorStore getVectorStore(VectorMetaData metaData) {
        Assert.notNull(metaData, "vector meta data must not be null");
        log.debug("vectorStoreMap keys are : {} and vectorMetaData : {}", vectorStoreMap.keySet(), metaData);
        String vectorStoreKey = VectorUtils.generateMilvusVectorStoreKey(metaData);
        if (vectorStoreMap.containsKey(vectorStoreKey)) {
            return vectorStoreMap.get(vectorStoreKey);
        } else {
            log.warn("vector store map does not contain instance for key : {}", vectorStoreKey);
            if (vectorStoreConfigJson.equals("[]") || vectorStoreConfigJson.equals("W10=")) {
                log.warn("property : 'commons.ai.sdk.vector_store.config' is not declared in your application.properties. You must declared the property to use vector store");
            } else {
                log.warn("going to create new vector store instance with vector meta data : {}", metaData);
                addVectorStoreDynamically(metaData);
                log.info("new vector store instance is added to map with key : {}", vectorStoreKey);
            }
            VectorStore vectorStore = vectorStoreMap.get(vectorStoreKey);
            Assert.notNull(vectorStore, "vector store must not be null");
            return vectorStore;
        }
    }

    public VectorStore getVectorStore(String vectorStoreId) {
        Assert.hasText(vectorStoreId, "vector store id must not be null or empty");
        log.debug("vectorIdMap keys are : {} and vectorStoreId : {}", vectorIdMap.keySet(), vectorStoreId);
        Map<String, Object> vectorMetaData = vectorIdMap.get(vectorStoreId);
        String vectorStoreKey = VectorUtils.generateMilvusVectorStoreKey(vectorMetaData);
        VectorStore vectorStore = vectorStoreMap.get(vectorStoreKey);
        Assert.notNull(vectorStore, String.format("vector store is null for the vectorStoreId : %s", vectorStoreId));
        return vectorStore;
    }

    private void addVectorStoreDynamically(VectorMetaData metaData) {
        List<Map<String, Object>> vectorStoreConfigJsonList = parseVectorStoreConfigJson();
        Map<String, Object> vectorStoreConfig = vectorStoreConfigJsonList.getFirst();
        vectorStoreConfig.put("collectionName", metaData.getCollectionName());
        vectorStoreConfig.put("inference", metaData.getProvider());
        vectorStoreConfig.put("embeddingModel", metaData.getEmbeddingModel());
        VectorStore newVectorStoreInstance = getMilvusVectorStoreInstance(vectorStoreConfig);
        String vectorStoreKey = VectorUtils.generateMilvusVectorStoreKey(vectorStoreConfig, milvusVectorStoreProperties);
        Map<String, VectorStore> newVectorStoreMap = new HashMap<>(vectorStoreMap);
        newVectorStoreMap.put(vectorStoreKey, newVectorStoreInstance);
        updateVectorStoreMap(newVectorStoreMap);
    }

    /**
     * Creates and configures a Milvus vector store instance based on the provided configuration.
     *
     * @param vectorStoreConfig Configuration map containing Milvus-specific settings
     * @return Configured MilvusVectorStore instance
     * @throws IllegalArgumentException if required configuration parameters are missing or invalid
     */
    private VectorStore getMilvusVectorStoreInstance(Map<String, Object> vectorStoreConfig) {
        MilvusVectorStoreProperties properties = ModelOptionsUtils.merge(vectorStoreConfig, milvusVectorStoreProperties, MilvusVectorStoreProperties.class);
        MilvusServiceClientProperties clientProperties = ModelOptionsUtils.merge(vectorStoreConfig, milvusServiceClientProperties, MilvusServiceClientProperties.class);

        var builder = ConnectParam.newBuilder()
                .withHost(clientProperties.getHost())
                .withPort(clientProperties.getPort())
                .withDatabaseName(properties.getDatabaseName())
                .withConnectTimeout(clientProperties.getConnectTimeoutMs(), TimeUnit.MILLISECONDS)
                .withKeepAliveTime(clientProperties.getKeepAliveTimeMs(), TimeUnit.MILLISECONDS)
                .withKeepAliveTimeout(clientProperties.getKeepAliveTimeoutMs(), TimeUnit.MILLISECONDS)
                .withRpcDeadline(clientProperties.getRpcDeadlineMs(), TimeUnit.MILLISECONDS)
                .withIdleTimeout(clientProperties.getIdleTimeoutMs(), TimeUnit.MILLISECONDS)
                .withAuthorization(clientProperties.getUsername(), clientProperties.getPassword());

        if (clientProperties.isSecure()) {
            PropertyMapper mapper = PropertyMapper.get();
            mapper.from(clientProperties::getUri).whenHasText().to(builder::withUri);
            mapper.from(clientProperties::getToken).whenHasText().to(builder::withToken);
            mapper.from(clientProperties::getClientKeyPath).whenHasText().to(builder::withClientKeyPath);
            mapper.from(clientProperties::getClientPemPath).whenHasText().to(builder::withClientPemPath);
            mapper.from(clientProperties::getCaPemPath).whenHasText().to(builder::withCaPemPath);
            mapper.from(clientProperties::getServerPemPath).whenHasText().to(builder::withServerPemPath);
            mapper.from(clientProperties::getServerName).whenHasText().to(builder::withServerName);
        }

        MilvusServiceClient milvusClient = new MilvusServiceClient(builder.build());

        String inference = (String) vectorStoreConfig.get("inference");
        String model = (String) vectorStoreConfig.get("embeddingModel");
        String key = String.format("%s_%s", inference, model);
        EmbeddingModel embeddingModel = inferenceManager.getEmbeddingModel();
        Assert.notNull(embeddingModel, String.format("embedding model is null for key %s", key));

        MilvusVectorStore milvusVectorStore = MilvusVectorStore.builder(milvusClient, embeddingModel)
                .initializeSchema(properties.isInitializeSchema())
                .databaseName(properties.getDatabaseName())
                .collectionName(properties.getCollectionName())
                .embeddingDimension(properties.getEmbeddingDimension())
                .indexType(IndexType.valueOf(properties.getIndexType().name()))
                .metricType(MetricType.valueOf(properties.getMetricType().name()))
                .indexParameters(properties.getIndexParameters())
                .iDFieldName(properties.getIdFieldName())
                .autoId(properties.isAutoId())
                .contentFieldName(properties.getContentFieldName())
                .metadataFieldName(properties.getMetadataFieldName())
                .embeddingFieldName(properties.getEmbeddingFieldName())
                .batchingStrategy(batchingStrategy)
                .observationRegistry(observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP))
                .customObservationConvention(customObservationConvention.getIfAvailable(() -> null))
                .build();

        try {
            milvusVectorStore.afterPropertiesSet();
        } catch (Exception e) {
            log.error("error while invoking milvusVectorStore.afterPropertiesSet()", e);
        }
        return milvusVectorStore;
    }

}